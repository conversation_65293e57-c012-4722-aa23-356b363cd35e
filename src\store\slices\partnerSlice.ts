import { createSlice, createAsyncThunk, PayloadAction } from '@reduxjs/toolkit';
import { DeliveryPartner } from '../../types';
import { partnerService } from '../../services/partnerService';

interface PartnerState {
  items: DeliveryPartner[];
  currentPartner: DeliveryPartner | null;
  isLoading: boolean;
  error: string | null;
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
  filters: {
    status?: string;
    vehicleType?: string;
    searchTerm?: string;
  };
}

const initialState: PartnerState = {
  items: [],
  currentPartner: null,
  isLoading: false,
  error: null,
  pagination: {
    page: 1,
    limit: 10,
    total: 0,
    totalPages: 0,
  },
  filters: {},
};

// Async thunks
export const registerPartner = createAsyncThunk(
  'partners/register',
  async (partnerData: Omit<DeliveryPartner, 'id' | 'createdAt' | 'status'>, { rejectWithValue }) => {
    try {
      const partner = await partnerService.registerPartner(partnerData);
      return partner;
    } catch (error: any) {
      return rejectWithValue(error.message || 'Failed to register partner');
    }
  }
);

export const fetchPartners = createAsyncThunk(
  'partners/fetchAll',
  async (params: {
    page?: number;
    limit?: number;
    status?: string;
    vehicleType?: string;
    searchTerm?: string;
  } = {}, { rejectWithValue }) => {
    try {
      const response = await partnerService.getPartners(params);
      return response;
    } catch (error: any) {
      return rejectWithValue(error.message || 'Failed to fetch partners');
    }
  }
);

export const updatePartnerStatus = createAsyncThunk(
  'partners/updateStatus',
  async ({ id, status }: { id: string; status: string }, { rejectWithValue }) => {
    try {
      const partner = await partnerService.updatePartnerStatus(id, status);
      return partner;
    } catch (error: any) {
      return rejectWithValue(error.message || 'Failed to update partner status');
    }
  }
);

export const getPartnerById = createAsyncThunk(
  'partners/getById',
  async (id: string, { rejectWithValue }) => {
    try {
      const partner = await partnerService.getPartnerById(id);
      return partner;
    } catch (error: any) {
      return rejectWithValue(error.message || 'Partner not found');
    }
  }
);

const partnerSlice = createSlice({
  name: 'partners',
  initialState,
  reducers: {
    clearError: (state) => {
      state.error = null;
    },
    setCurrentPartner: (state, action: PayloadAction<DeliveryPartner | null>) => {
      state.currentPartner = action.payload;
    },
    updateFilters: (state, action: PayloadAction<Partial<PartnerState['filters']>>) => {
      state.filters = { ...state.filters, ...action.payload };
    },
    clearFilters: (state) => {
      state.filters = {};
    },
    setPagination: (state, action: PayloadAction<Partial<PartnerState['pagination']>>) => {
      state.pagination = { ...state.pagination, ...action.payload };
    },
  },
  extraReducers: (builder) => {
    builder
      // Register partner
      .addCase(registerPartner.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(registerPartner.fulfilled, (state, action) => {
        state.isLoading = false;
        state.items.unshift(action.payload);
        state.currentPartner = action.payload;
      })
      .addCase(registerPartner.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
      })
      // Fetch partners
      .addCase(fetchPartners.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(fetchPartners.fulfilled, (state, action) => {
        state.isLoading = false;
        state.items = action.payload.partners;
        state.pagination = action.payload.pagination;
      })
      .addCase(fetchPartners.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
      })
      // Update partner status
      .addCase(updatePartnerStatus.fulfilled, (state, action) => {
        const index = state.items.findIndex(item => item.id === action.payload.id);
        if (index !== -1) {
          state.items[index] = action.payload;
        }
        if (state.currentPartner?.id === action.payload.id) {
          state.currentPartner = action.payload;
        }
      })
      // Get partner by ID
      .addCase(getPartnerById.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(getPartnerById.fulfilled, (state, action) => {
        state.isLoading = false;
        state.currentPartner = action.payload;
      })
      .addCase(getPartnerById.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
        state.currentPartner = null;
      });
  },
});

export const {
  clearError,
  setCurrentPartner,
  updateFilters,
  clearFilters,
  setPagination,
} = partnerSlice.actions;

export default partnerSlice.reducer;
