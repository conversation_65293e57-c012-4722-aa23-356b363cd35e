import { apiService } from './api';
import { DeliveryPartner } from '../types';

interface RegisterPartnerData {
  name: string;
  email: string;
  phone: string;
  vehicleType: string;
  vehicleNumber: string;
  licenseNumber: string;
  aadharNumber: string;
  bankAccount: string;
  ifscCode: string;
  address: string;
  city: string;
  pincode: string;
  documentsUploaded: boolean;
}

interface PartnerFilters {
  page?: number;
  limit?: number;
  status?: string;
  vehicleType?: string;
  searchTerm?: string;
  city?: string;
}

interface PartnersResponse {
  partners: DeliveryPartner[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
}

interface DocumentUpload {
  type: 'license' | 'aadhar' | 'vehicle_registration' | 'bank_details';
  file: File;
}

export const partnerService = {
  // Register new partner
  registerPartner: async (partnerData: RegisterPartnerData): Promise<DeliveryPartner> => {
    try {
      const response = await apiService.post<DeliveryPartner>('/partners/register', partnerData);
      return response;
    } catch (error) {
      throw error;
    }
  },

  // Get partners with filters and pagination
  getPartners: async (filters: PartnerFilters = {}): Promise<PartnersResponse> => {
    try {
      const queryParams = new URLSearchParams();
      
      Object.entries(filters).forEach(([key, value]) => {
        if (value !== undefined && value !== null) {
          queryParams.append(key, value.toString());
        }
      });

      const response = await apiService.get<PartnersResponse>(
        `/partners?${queryParams.toString()}`
      );
      return response;
    } catch (error) {
      throw error;
    }
  },

  // Get partner by ID
  getPartnerById: async (id: string): Promise<DeliveryPartner> => {
    try {
      const response = await apiService.get<DeliveryPartner>(`/partners/${id}`);
      return response;
    } catch (error) {
      throw error;
    }
  },

  // Update partner status
  updatePartnerStatus: async (id: string, status: string): Promise<DeliveryPartner> => {
    try {
      const response = await apiService.patch<DeliveryPartner>(`/partners/${id}/status`, {
        status,
      });
      return response;
    } catch (error) {
      throw error;
    }
  },

  // Update partner profile
  updatePartnerProfile: async (id: string, profileData: Partial<DeliveryPartner>): Promise<DeliveryPartner> => {
    try {
      const response = await apiService.patch<DeliveryPartner>(`/partners/${id}`, profileData);
      return response;
    } catch (error) {
      throw error;
    }
  },

  // Upload partner documents
  uploadDocument: async (partnerId: string, document: DocumentUpload): Promise<{
    message: string;
    documentUrl: string;
  }> => {
    try {
      const formData = new FormData();
      formData.append('type', document.type);
      formData.append('file', document.file);

      const response = await apiService.upload(
        `/partners/${partnerId}/documents`,
        formData
      );
      return response;
    } catch (error) {
      throw error;
    }
  },

  // Get partner documents
  getPartnerDocuments: async (partnerId: string): Promise<{
    documents: Array<{
      type: string;
      url: string;
      uploadedAt: Date;
      verified: boolean;
    }>;
  }> => {
    try {
      const response = await apiService.get(`/partners/${partnerId}/documents`);
      return response;
    } catch (error) {
      throw error;
    }
  },

  // Verify partner document
  verifyDocument: async (partnerId: string, documentType: string, verified: boolean): Promise<{
    message: string;
  }> => {
    try {
      const response = await apiService.patch(`/partners/${partnerId}/documents/${documentType}/verify`, {
        verified,
      });
      return response;
    } catch (error) {
      throw error;
    }
  },

  // Get available partners for assignment
  getAvailablePartners: async (filters?: {
    vehicleType?: string;
    location?: { lat: number; lng: number };
    radius?: number;
  }): Promise<DeliveryPartner[]> => {
    try {
      const queryParams = new URLSearchParams();
      
      if (filters?.vehicleType) {
        queryParams.append('vehicleType', filters.vehicleType);
      }
      
      if (filters?.location) {
        queryParams.append('lat', filters.location.lat.toString());
        queryParams.append('lng', filters.location.lng.toString());
      }
      
      if (filters?.radius) {
        queryParams.append('radius', filters.radius.toString());
      }

      const response = await apiService.get<DeliveryPartner[]>(
        `/partners/available?${queryParams.toString()}`
      );
      return response;
    } catch (error) {
      throw error;
    }
  },

  // Get partner statistics
  getPartnerStats: async (partnerId?: string): Promise<{
    totalDeliveries: number;
    completedDeliveries: number;
    cancelledDeliveries: number;
    averageRating: number;
    totalEarnings: number;
    thisMonthEarnings: number;
    onTimeDeliveryRate: number;
  }> => {
    try {
      const url = partnerId ? `/partners/${partnerId}/stats` : '/partners/stats';
      const response = await apiService.get(url);
      return response;
    } catch (error) {
      throw error;
    }
  },

  // Update partner location (for real-time tracking)
  updateLocation: async (partnerId: string, location: {
    lat: number;
    lng: number;
    heading?: number;
    speed?: number;
  }): Promise<{ message: string }> => {
    try {
      const response = await apiService.post(`/partners/${partnerId}/location`, location);
      return response;
    } catch (error) {
      throw error;
    }
  },

  // Get partner earnings
  getPartnerEarnings: async (partnerId: string, filters?: {
    startDate?: Date;
    endDate?: Date;
    page?: number;
    limit?: number;
  }): Promise<{
    earnings: Array<{
      id: string;
      parcelId: string;
      amount: number;
      commission: number;
      netAmount: number;
      paidAt: Date;
      status: 'pending' | 'paid';
    }>;
    totalEarnings: number;
    pendingAmount: number;
    pagination: {
      page: number;
      limit: number;
      total: number;
      totalPages: number;
    };
  }> => {
    try {
      const queryParams = new URLSearchParams();
      
      if (filters) {
        Object.entries(filters).forEach(([key, value]) => {
          if (value !== undefined && value !== null) {
            if (value instanceof Date) {
              queryParams.append(key, value.toISOString());
            } else {
              queryParams.append(key, value.toString());
            }
          }
        });
      }

      const response = await apiService.get(
        `/partners/${partnerId}/earnings?${queryParams.toString()}`
      );
      return response;
    } catch (error) {
      throw error;
    }
  },
};
