import {
  generateTrackingId,
  formatCurrency,
  formatDate,
  calculateDeliveryFee,
  getDeliveryTimeEstimate,
  validatePhoneNumber,
  validateEmail,
} from '../helpers';

describe('Helper Functions', () => {
  describe('generateTrackingId', () => {
    it('generates a tracking ID with correct format', () => {
      const trackingId = generateTrackingId();
      expect(trackingId).toMatch(/^QD\d{6}[A-Z0-9]{4}$/);
    });

    it('generates unique tracking IDs', () => {
      const id1 = generateTrackingId();
      const id2 = generateTrackingId();
      expect(id1).not.toBe(id2);
    });

    it('always starts with QD prefix', () => {
      const trackingId = generateTrackingId();
      expect(trackingId.startsWith('QD')).toBe(true);
    });
  });

  describe('formatCurrency', () => {
    it('formats currency in INR format', () => {
      expect(formatCurrency(100)).toBe('₹100.00');
      expect(formatCurrency(1234.56)).toBe('₹1,234.56');
      expect(formatCurrency(0)).toBe('₹0.00');
    });

    it('handles decimal values correctly', () => {
      expect(formatCurrency(99.99)).toBe('₹99.99');
      expect(formatCurrency(100.1)).toBe('₹100.10');
    });
  });

  describe('formatDate', () => {
    it('formats date in Indian format', () => {
      const date = new Date('2023-12-25T10:30:00Z');
      const formatted = formatDate(date);
      expect(formatted).toMatch(/25 Dec 2023/);
    });

    it('includes time in the format', () => {
      const date = new Date('2023-12-25T10:30:00Z');
      const formatted = formatDate(date);
      expect(formatted).toMatch(/\d{2}:\d{2}/);
    });
  });

  describe('calculateDeliveryFee', () => {
    it('calculates basic delivery fee correctly', () => {
      const fee = calculateDeliveryFee(1, 5); // 1kg, 5km
      expect(fee).toBe(50); // 30 + (1*5) + (5*2) = 45, but minimum is 50
    });

    it('applies minimum fee of 50', () => {
      const fee = calculateDeliveryFee(0.5, 1); // 0.5kg, 1km
      expect(fee).toBe(50); // Should be minimum 50
    });

    it('calculates higher fees for heavier packages', () => {
      const fee1 = calculateDeliveryFee(1, 5);
      const fee2 = calculateDeliveryFee(5, 5);
      expect(fee2).toBeGreaterThan(fee1);
    });

    it('calculates higher fees for longer distances', () => {
      const fee1 = calculateDeliveryFee(1, 5);
      const fee2 = calculateDeliveryFee(1, 10);
      expect(fee2).toBeGreaterThan(fee1);
    });
  });

  describe('getDeliveryTimeEstimate', () => {
    it('returns correct time estimates for different distances', () => {
      expect(getDeliveryTimeEstimate(3)).toBe('30-60 minutes');
      expect(getDeliveryTimeEstimate(7)).toBe('1-2 hours');
      expect(getDeliveryTimeEstimate(15)).toBe('2-4 hours');
      expect(getDeliveryTimeEstimate(25)).toBe('Same day delivery');
    });

    it('handles edge cases correctly', () => {
      expect(getDeliveryTimeEstimate(5)).toBe('30-60 minutes');
      expect(getDeliveryTimeEstimate(10)).toBe('1-2 hours');
      expect(getDeliveryTimeEstimate(20)).toBe('2-4 hours');
    });
  });

  describe('validatePhoneNumber', () => {
    it('validates correct Indian phone numbers', () => {
      expect(validatePhoneNumber('9876543210')).toBe(true);
      expect(validatePhoneNumber('8123456789')).toBe(true);
      expect(validatePhoneNumber('7000000000')).toBe(true);
      expect(validatePhoneNumber('6999999999')).toBe(true);
    });

    it('rejects invalid phone numbers', () => {
      expect(validatePhoneNumber('1234567890')).toBe(false); // Doesn't start with 6-9
      expect(validatePhoneNumber('98765432')).toBe(false); // Too short
      expect(validatePhoneNumber('98765432101')).toBe(false); // Too long
      expect(validatePhoneNumber('abcdefghij')).toBe(false); // Not numeric
      expect(validatePhoneNumber('')).toBe(false); // Empty
    });
  });

  describe('validateEmail', () => {
    it('validates correct email addresses', () => {
      expect(validateEmail('<EMAIL>')).toBe(true);
      expect(validateEmail('<EMAIL>')).toBe(true);
      expect(validateEmail('<EMAIL>')).toBe(true);
    });

    it('rejects invalid email addresses', () => {
      expect(validateEmail('invalid-email')).toBe(false);
      expect(validateEmail('test@')).toBe(false);
      expect(validateEmail('@example.com')).toBe(false);
      expect(validateEmail('')).toBe(false);
    });
  });
});
