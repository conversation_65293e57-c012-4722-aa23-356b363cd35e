import React, { useEffect, useRef, useState } from 'react';
import { config } from '../config/env';
import MapFallback from './MapFallback';

// Declare global google object
declare global {
  interface Window {
    google: typeof google;
  }
}

interface GoogleMapProps {
  center: { lat: number; lng: number };
  zoom?: number;
  markers?: Array<{
    position: { lat: number; lng: number };
    title?: string;
    info?: string;
    icon?: string;
  }>;
  className?: string;
  onMapClick?: (event: any) => void;
  onMarkerClick?: (marker: any, index: number) => void;
  showTraffic?: boolean;
  mapType?: string;
}

const GoogleMap: React.FC<GoogleMapProps> = ({
  center,
  zoom = 13,
  markers = [],
  className = "w-full h-96",
  onMapClick,
  onMarkerClick,
  showTraffic = false,
  mapType = 'roadmap'
}) => {
  const mapRef = useRef<HTMLDivElement>(null);
  const mapInstanceRef = useRef<google.maps.Map | null>(null);
  const markersRef = useRef<google.maps.Marker[]>([]);
  const trafficLayerRef = useRef<google.maps.TrafficLayer | null>(null);
  const [isLoaded, setIsLoaded] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Load Google Maps API
  useEffect(() => {
    if (!config.GOOGLE_MAPS_API_KEY) {
      setError('Google Maps API key not configured');
      return;
    }

    if (window.google?.maps) {
      setIsLoaded(true);
      return;
    }

    const script = document.createElement('script');
    script.src = `https://maps.googleapis.com/maps/api/js?key=${config.GOOGLE_MAPS_API_KEY}&libraries=places,geometry`;
    script.async = true;
    script.defer = true;

    script.onload = () => {
      setIsLoaded(true);
    };

    script.onerror = () => {
      setError('Failed to load Google Maps API');
    };

    document.head.appendChild(script);

    return () => {
      if (document.head.contains(script)) {
        document.head.removeChild(script);
      }
    };
  }, []);

  // Initialize map
  useEffect(() => {
    if (!isLoaded || !mapRef.current || mapInstanceRef.current || !window.google?.maps) return;

    try {
      mapInstanceRef.current = new window.google.maps.Map(mapRef.current, {
        center,
        zoom,
        mapTypeId: window.google.maps.MapTypeId.ROADMAP,
        styles: [
          {
            featureType: 'poi',
            elementType: 'labels',
            stylers: [{ visibility: 'off' }]
          }
        ],
        mapTypeControl: true,
        streetViewControl: true,
        fullscreenControl: true,
        zoomControl: true,
      });

      // Add click listener
      if (onMapClick) {
        mapInstanceRef.current.addListener('click', onMapClick);
      }

      // Initialize traffic layer
      if (showTraffic) {
        trafficLayerRef.current = new window.google.maps.TrafficLayer();
        trafficLayerRef.current.setMap(mapInstanceRef.current);
      }
    } catch (err) {
      setError('Failed to initialize Google Maps');
      console.error('Google Maps initialization error:', err);
    }
  }, [isLoaded, center, zoom, mapType, onMapClick, showTraffic]);

  // Update map center and zoom
  useEffect(() => {
    if (mapInstanceRef.current) {
      mapInstanceRef.current.setCenter(center);
      mapInstanceRef.current.setZoom(zoom);
    }
  }, [center, zoom]);

  // Update markers
  useEffect(() => {
    if (!mapInstanceRef.current || !isLoaded || !window.google?.maps) return;

    // Clear existing markers
    markersRef.current.forEach(marker => marker.setMap(null));
    markersRef.current = [];

    // Add new markers
    markers.forEach((markerData, index) => {
      try {
        const marker = new window.google.maps.Marker({
          position: markerData.position,
          map: mapInstanceRef.current,
          title: markerData.title,
          icon: markerData.icon,
        });

        // Add info window if info is provided
        if (markerData.info) {
          const infoWindow = new window.google.maps.InfoWindow({
            content: markerData.info,
          });

          marker.addListener('click', () => {
            infoWindow.open(mapInstanceRef.current, marker);
            onMarkerClick?.(marker, index);
          });
        } else if (onMarkerClick) {
          marker.addListener('click', () => {
            onMarkerClick(marker, index);
          });
        }

        markersRef.current.push(marker);
      } catch (err) {
        console.error('Error creating marker:', err);
      }
    });
  }, [markers, isLoaded, onMarkerClick]);

  // Toggle traffic layer
  useEffect(() => {
    if (!mapInstanceRef.current || !isLoaded || !window.google?.maps) return;

    if (showTraffic && !trafficLayerRef.current) {
      trafficLayerRef.current = new window.google.maps.TrafficLayer();
      trafficLayerRef.current.setMap(mapInstanceRef.current);
    } else if (!showTraffic && trafficLayerRef.current) {
      trafficLayerRef.current.setMap(null);
      trafficLayerRef.current = null;
    }
  }, [showTraffic, isLoaded]);

  // Fallback UI for when Google Maps is not available
  if (error || !config.FEATURES.GOOGLE_MAPS) {
    return (
      <MapFallback
        center={center}
        markers={markers}
        className={className}
        showTraffic={showTraffic}
      />
    );
  }

  // Loading state
  if (!isLoaded) {
    return (
      <div className={className}>
        <div className="w-full h-full bg-gray-100 rounded-lg flex items-center justify-center">
          <div className="text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
            <p className="text-gray-600">Loading map...</p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className={className}>
      <div ref={mapRef} className="w-full h-full rounded-lg" />
    </div>
  );
};

export default GoogleMap;