import React, { useEffect, useRef } from 'react';

interface GoogleMapProps {
  center: { lat: number; lng: number };
  zoom?: number;
  markers?: Array<{
    position: { lat: number; lng: number };
    title?: string;
    info?: string;
  }>;
  className?: string;
}

const GoogleMap: React.FC<GoogleMapProps> = ({ 
  center, 
  zoom = 13, 
  markers = [], 
  className = "w-full h-96" 
}) => {
  const mapRef = useRef<HTMLDivElement>(null);
  const mapInstanceRef = useRef<google.maps.Map | null>(null);

  useEffect(() => {
    // Note: In production, you would load the Google Maps API
    // For demo purposes, we'll show a placeholder
    if (mapRef.current && !mapInstanceRef.current) {
      // This would normally initialize Google Maps
      console.log('Google Maps would be initialized here with center:', center);
    }
  }, [center]);

  return (
    <div className={className}>
      <div 
        ref={mapRef} 
        className="w-full h-full bg-gray-200 rounded-lg flex items-center justify-center border-2 border-dashed border-gray-300"
      >
        <div className="text-center p-8">
          <div className="text-4xl mb-4">🗺️</div>
          <p className="text-gray-600 font-medium">Google Maps Integration</p>
          <p className="text-sm text-gray-500 mt-2">
            Map would display service areas and tracking here
          </p>
          <p className="text-xs text-gray-400 mt-2">
            Center: {center.lat.toFixed(4)}, {center.lng.toFixed(4)}
          </p>
          {markers.length > 0 && (
            <p className="text-xs text-gray-400">
              {markers.length} marker(s) would be shown
            </p>
          )}
        </div>
      </div>
    </div>
  );
};

export default GoogleMap;