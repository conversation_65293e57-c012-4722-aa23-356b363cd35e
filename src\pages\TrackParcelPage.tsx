import React, { useState } from 'react';
import { Search, Package2, MapPin, Clock, CheckCircle, Truck, User, Phone } from 'lucide-react';
import GoogleMap from '../components/GoogleMap';
import { formatDate } from '../utils/helpers';

interface TrackingStatus {
  status: string;
  timestamp: Date;
  location: string;
  description: string;
  completed: boolean;
}

interface ParcelInfo {
  trackingId: string;
  senderName: string;
  senderPhone: string;
  receiverName: string;
  receiverPhone: string;
  currentStatus: string;
  currentLocation: { lat: number; lng: number; address: string };
  estimatedDelivery: Date;
  timeline: TrackingStatus[];
}

const TrackParcelPage: React.FC = () => {
  const [trackingId, setTrackingId] = useState('');
  const [isSearching, setIsSearching] = useState(false);
  const [parcelInfo, setParcelInfo] = useState<ParcelInfo | null>(null);
  const [error, setError] = useState('');

  // Mock tracking data
  const mockParcelInfo: ParcelInfo = {
    trackingId: 'QD123456ABCD',
    senderName: '<PERSON>',
    senderPhone: '+91 98765 43210',
    receiverName: 'Jane Smith',
    receiverPhone: '+91 87654 32109',
    currentStatus: 'in_transit',
    currentLocation: { 
      lat: 28.6139, 
      lng: 77.2090, 
      address: 'Connaught Place, New Delhi' 
    },
    estimatedDelivery: new Date(Date.now() + 2 * 60 * 60 * 1000), // 2 hours from now
    timeline: [
      {
        status: 'Order Placed',
        timestamp: new Date(Date.now() - 4 * 60 * 60 * 1000),
        location: 'Customer Location',
        description: 'Your parcel booking has been confirmed',
        completed: true
      },
      {
        status: 'Picked Up',
        timestamp: new Date(Date.now() - 3 * 60 * 60 * 1000),
        location: 'Pickup Address',
        description: 'Parcel has been collected from sender',
        completed: true
      },
      {
        status: 'In Transit',
        timestamp: new Date(Date.now() - 1 * 60 * 60 * 1000),
        location: 'Hub - Central Delhi',
        description: 'Package is on the way to destination',
        completed: true
      },
      {
        status: 'Out for Delivery',
        timestamp: new Date(),
        location: 'Delivery Vehicle',
        description: 'Your parcel is out for delivery',
        completed: false
      },
      {
        status: 'Delivered',
        timestamp: new Date(Date.now() + 2 * 60 * 60 * 1000),
        location: 'Destination',
        description: 'Parcel successfully delivered',
        completed: false
      }
    ]
  };

  const handleSearch = async () => {
    if (!trackingId.trim()) {
      setError('Please enter a tracking ID');
      return;
    }

    setIsSearching(true);
    setError('');

    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 1500));

    if (trackingId.toLowerCase().includes('qd') || trackingId === 'demo') {
      setParcelInfo(mockParcelInfo);
    } else {
      setError('Tracking ID not found. Please check and try again.');
      setParcelInfo(null);
    }

    setIsSearching(false);
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      handleSearch();
    }
  };

  const getStatusColor = (status: string) => {
    switch (status.toLowerCase()) {
      case 'pending':
        return 'text-yellow-600 bg-yellow-100';
      case 'picked_up':
        return 'text-blue-600 bg-blue-100';
      case 'in_transit':
        return 'text-purple-600 bg-purple-100';
      case 'out_for_delivery':
        return 'text-orange-600 bg-orange-100';
      case 'delivered':
        return 'text-green-600 bg-green-100';
      default:
        return 'text-gray-600 bg-gray-100';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status.toLowerCase()) {
      case 'order placed':
        return <Package2 className="h-5 w-5" />;
      case 'picked up':
        return <CheckCircle className="h-5 w-5" />;
      case 'in transit':
        return <Truck className="h-5 w-5" />;
      case 'out for delivery':
        return <MapPin className="h-5 w-5" />;
      case 'delivered':
        return <CheckCircle className="h-5 w-5" />;
      default:
        return <Clock className="h-5 w-5" />;
    }
  };

  return (
    <div className="min-h-screen bg-gray-50 py-12">
      <div className="max-w-4xl mx-auto px-4">
        {/* Header */}
        <div className="text-center mb-8">
          <h1 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
            Track Your Package
          </h1>
          <p className="text-xl text-gray-600">
            Enter your tracking ID to get real-time updates
          </p>
        </div>

        {/* Search Section */}
        <div className="bg-white rounded-xl shadow-lg p-8 mb-8">
          <div className="flex flex-col md:flex-row gap-4">
            <div className="flex-1">
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Tracking ID
              </label>
              <input
                type="text"
                value={trackingId}
                onChange={(e) => setTrackingId(e.target.value)}
                onKeyPress={handleKeyPress}
                placeholder="Enter your tracking ID (e.g., QD123456ABCD or 'demo')"
                className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              />
            </div>
            <div className="flex items-end">
              <button
                onClick={handleSearch}
                disabled={isSearching}
                className="bg-blue-600 text-white px-8 py-3 rounded-lg font-medium hover:bg-blue-700 transition-colors disabled:bg-blue-400 flex items-center space-x-2"
              >
                {isSearching ? (
                  <>
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                    <span>Searching...</span>
                  </>
                ) : (
                  <>
                    <Search className="h-4 w-4" />
                    <span>Track</span>
                  </>
                )}
              </button>
            </div>
          </div>

          {error && (
            <div className="mt-4 p-4 bg-red-50 border border-red-200 rounded-lg">
              <p className="text-red-700">{error}</p>
            </div>
          )}

          <div className="mt-4 text-sm text-gray-600">
            <p className="mb-2">💡 <strong>Demo:</strong> Try searching with "demo" or any ID containing "QD"</p>
            <p>Don't have a tracking ID? <a href="/book" className="text-blue-600 hover:underline">Book a delivery</a> to get one.</p>
          </div>
        </div>

        {/* Tracking Results */}
        {parcelInfo && (
          <div className="space-y-8">
            {/* Current Status */}
            <div className="bg-white rounded-xl shadow-lg p-8">
              <div className="flex items-center justify-between mb-6">
                <h2 className="text-2xl font-bold text-gray-900">Current Status</h2>
                <span className={`px-4 py-2 rounded-full text-sm font-medium ${getStatusColor(parcelInfo.currentStatus)}`}>
                  {parcelInfo.currentStatus.replace('_', ' ').toUpperCase()}
                </span>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
                <div className="space-y-4">
                  <div>
                    <h3 className="font-semibold text-gray-900 mb-2">Package Details</h3>
                    <div className="space-y-2 text-sm">
                      <p><span className="font-medium">Tracking ID:</span> {parcelInfo.trackingId}</p>
                      <p><span className="font-medium">Current Location:</span> {parcelInfo.currentLocation.address}</p>
                      <p><span className="font-medium">Expected Delivery:</span> {formatDate(parcelInfo.estimatedDelivery)}</p>
                    </div>
                  </div>

                  <div className="grid grid-cols-2 gap-4">
                    <div className="bg-gray-50 p-4 rounded-lg">
                      <div className="flex items-center space-x-2 mb-2">
                        <User className="h-4 w-4 text-gray-500" />
                        <span className="font-medium text-gray-900">Sender</span>
                      </div>
                      <p className="text-sm text-gray-600">{parcelInfo.senderName}</p>
                      <div className="flex items-center space-x-1 mt-1">
                        <Phone className="h-3 w-3 text-gray-400" />
                        <p className="text-xs text-gray-500">{parcelInfo.senderPhone}</p>
                      </div>
                    </div>

                    <div className="bg-gray-50 p-4 rounded-lg">
                      <div className="flex items-center space-x-2 mb-2">
                        <MapPin className="h-4 w-4 text-gray-500" />
                        <span className="font-medium text-gray-900">Receiver</span>
                      </div>
                      <p className="text-sm text-gray-600">{parcelInfo.receiverName}</p>
                      <div className="flex items-center space-x-1 mt-1">
                        <Phone className="h-3 w-3 text-gray-400" />
                        <p className="text-xs text-gray-500">{parcelInfo.receiverPhone}</p>
                      </div>
                    </div>
                  </div>
                </div>

                <div>
                  <h3 className="font-semibold text-gray-900 mb-4">Live Location</h3>
                  <GoogleMap
                    center={parcelInfo.currentLocation}
                    markers={[{
                      position: parcelInfo.currentLocation,
                      title: 'Current Location',
                      info: parcelInfo.currentLocation.address
                    }]}
                    className="h-64 w-full"
                  />
                </div>
              </div>
            </div>

            {/* Timeline */}
            <div className="bg-white rounded-xl shadow-lg p-8">
              <h2 className="text-2xl font-bold text-gray-900 mb-6">Delivery Timeline</h2>
              
              <div className="space-y-4">
                {parcelInfo.timeline.map((item, index) => (
                  <div key={index} className="flex items-start space-x-4">
                    <div className={`flex-shrink-0 w-10 h-10 rounded-full flex items-center justify-center ${
                      item.completed 
                        ? 'bg-green-100 text-green-600' 
                        : index === parcelInfo.timeline.findIndex(t => !t.completed)
                          ? 'bg-blue-100 text-blue-600'
                          : 'bg-gray-100 text-gray-400'
                    }`}>
                      {getStatusIcon(item.status)}
                    </div>

                    <div className="flex-1 min-w-0">
                      <div className="flex items-center justify-between">
                        <h3 className={`font-semibold ${
                          item.completed ? 'text-gray-900' : 'text-gray-600'
                        }`}>
                          {item.status}
                        </h3>
                        <span className={`text-sm ${
                          item.completed ? 'text-gray-600' : 'text-gray-400'
                        }`}>
                          {formatDate(item.timestamp)}
                        </span>
                      </div>
                      <p className={`text-sm mt-1 ${
                        item.completed ? 'text-gray-600' : 'text-gray-400'
                      }`}>
                        {item.description}
                      </p>
                      <p className={`text-xs mt-1 ${
                        item.completed ? 'text-gray-500' : 'text-gray-400'
                      }`}>
                        📍 {item.location}
                      </p>
                    </div>

                    {item.completed && (
                      <CheckCircle className="h-5 w-5 text-green-500 flex-shrink-0 mt-1" />
                    )}
                  </div>
                ))}
              </div>
            </div>

            {/* Contact Support */}
            <div className="bg-gradient-to-r from-blue-50 to-emerald-50 rounded-xl p-8">
              <div className="text-center">
                <h3 className="text-xl font-bold text-gray-900 mb-2">Need Help?</h3>
                <p className="text-gray-600 mb-4">
                  Our support team is here to assist you with any questions about your delivery.
                </p>
                <div className="flex flex-col sm:flex-row gap-4 justify-center">
                  <a
                    href="tel:+919876543210"
                    className="bg-blue-600 text-white px-6 py-3 rounded-lg font-medium hover:bg-blue-700 transition-colors inline-flex items-center justify-center space-x-2"
                  >
                    <Phone className="h-4 w-4" />
                    <span>Call Support</span>
                  </a>
                  <a
                    href="mailto:<EMAIL>"
                    className="border border-blue-600 text-blue-600 px-6 py-3 rounded-lg font-medium hover:bg-blue-50 transition-colors"
                  >
                    Email Us
                  </a>
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default TrackParcelPage;