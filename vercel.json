{"buildCommand": "npm run build", "outputDirectory": "dist", "framework": "vite", "installCommand": "npm install", "devCommand": "npm run dev", "env": {"VITE_API_BASE_URL": "@vite_api_base_url", "VITE_GOOGLE_MAPS_API_KEY": "@vite_google_maps_api_key", "VITE_FIREBASE_API_KEY": "@vite_firebase_api_key", "VITE_FIREBASE_AUTH_DOMAIN": "@vite_firebase_auth_domain", "VITE_FIREBASE_PROJECT_ID": "@vite_firebase_project_id", "VITE_FIREBASE_STORAGE_BUCKET": "@vite_firebase_storage_bucket", "VITE_FIREBASE_MESSAGING_SENDER_ID": "@vite_firebase_messaging_sender_id", "VITE_FIREBASE_APP_ID": "@vite_firebase_app_id", "VITE_STRIPE_PUBLISHABLE_KEY": "@vite_stripe_publishable_key"}, "routes": [{"src": "/api/(.*)", "dest": "/api/$1"}, {"src": "/(.*)", "dest": "/index.html"}], "headers": [{"source": "/(.*)", "headers": [{"key": "X-Content-Type-Options", "value": "nosniff"}, {"key": "X-Frame-Options", "value": "DENY"}, {"key": "X-XSS-Protection", "value": "1; mode=block"}, {"key": "Referrer-Policy", "value": "strict-origin-when-cross-origin"}]}]}