import React from 'react';
import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
import Layout from './components/Layout';
import HomePage from './pages/HomePage';
import BookParcelPage from './pages/BookParcelPage';
import TrackParcelPage from './pages/TrackParcelPage';
import MobileAppPage from './pages/MobileAppPage';
import PartnerRegistrationPage from './pages/PartnerRegistrationPage';
import AdminDashboard from './pages/AdminDashboard';

function App() {
  return (
    <Router>
      <Routes>
        <Route path="/" element={<Layout />}>
          <Route index element={<HomePage />} />
          <Route path="book" element={<BookParcelPage />} />
          <Route path="track" element={<TrackParcelPage />} />
          <Route path="app" element={<MobileAppPage />} />
          <Route path="partner" element={<PartnerRegistrationPage />} />
          <Route path="admin" element={<AdminDashboard />} />
        </Route>
      </Routes>
    </Router>
  );
}

export default App;