import React, { useState } from 'react';
import { Package2, MapPin, User, Phone, Calendar, CreditCard, ArrowRight } from 'lucide-react';
import { PACKAGE_TYPES } from '../utils/constants';
import { generateTrackingId, calculateDeliveryFee, getDeliveryTimeEstimate, validatePhoneNumber, validateEmail } from '../utils/helpers';

interface BookingForm {
  // Sender Details
  senderName: string;
  senderPhone: string;
  senderEmail: string;
  senderAddress: string;
  
  // Receiver Details
  receiverName: string;
  receiverPhone: string;
  receiverAddress: string;
  
  // Package Details
  packageType: string;
  weight: number;
  length: number;
  width: number;
  height: number;
  description: string;
  
  // Delivery Details
  pickupDate: string;
  pickupTime: string;
  deliveryInstructions: string;
}

const BookParcelPage: React.FC = () => {
  const [currentStep, setCurrentStep] = useState(1);
  const [formData, setFormData] = useState<BookingForm>({
    senderName: '',
    senderPhone: '',
    senderEmail: '',
    senderAddress: '',
    receiverName: '',
    receiverPhone: '',
    receiverAddress: '',
    packageType: '',
    weight: 0,
    length: 0,
    width: 0,
    height: 0,
    description: '',
    pickupDate: '',
    pickupTime: '',
    deliveryInstructions: ''
  });

  const [errors, setErrors] = useState<Partial<BookingForm>>({});
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [bookingConfirmed, setBookingConfirmed] = useState(false);
  const [trackingId, setTrackingId] = useState('');

  const handleInputChange = (field: keyof BookingForm, value: string | number) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
    
    // Clear error when user starts typing
    if (errors[field]) {
      setErrors(prev => ({
        ...prev,
        [field]: ''
      }));
    }
  };

  const validateStep = (step: number): boolean => {
    const newErrors: Partial<BookingForm> = {};

    if (step === 1) {
      if (!formData.senderName) newErrors.senderName = 'Name is required';
      if (!formData.senderPhone) {
        newErrors.senderPhone = 'Phone number is required';
      } else if (!validatePhoneNumber(formData.senderPhone)) {
        newErrors.senderPhone = 'Invalid phone number';
      }
      if (!formData.senderEmail) {
        newErrors.senderEmail = 'Email is required';
      } else if (!validateEmail(formData.senderEmail)) {
        newErrors.senderEmail = 'Invalid email address';
      }
      if (!formData.senderAddress) newErrors.senderAddress = 'Address is required';
    }

    if (step === 2) {
      if (!formData.receiverName) newErrors.receiverName = 'Name is required';
      if (!formData.receiverPhone) {
        newErrors.receiverPhone = 'Phone number is required';
      } else if (!validatePhoneNumber(formData.receiverPhone)) {
        newErrors.receiverPhone = 'Invalid phone number';
      }
      if (!formData.receiverAddress) newErrors.receiverAddress = 'Address is required';
    }

    if (step === 3) {
      if (!formData.packageType) newErrors.packageType = 'Package type is required';
      if (!formData.weight || formData.weight <= 0) newErrors.weight = 'Weight must be greater than 0';
      if (!formData.length || formData.length <= 0) newErrors.length = 'Length is required';
      if (!formData.width || formData.width <= 0) newErrors.width = 'Width is required';
      if (!formData.height || formData.height <= 0) newErrors.height = 'Height is required';
    }

    if (step === 4) {
      if (!formData.pickupDate) newErrors.pickupDate = 'Pickup date is required';
      if (!formData.pickupTime) newErrors.pickupTime = 'Pickup time is required';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleNext = () => {
    if (validateStep(currentStep)) {
      setCurrentStep(prev => prev + 1);
    }
  };

  const handleSubmit = async () => {
    if (!validateStep(4)) return;

    setIsSubmitting(true);
    
    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 2000));
    
    const newTrackingId = generateTrackingId();
    setTrackingId(newTrackingId);
    setBookingConfirmed(true);
    setIsSubmitting(false);
  };

  const deliveryFee = calculateDeliveryFee(formData.weight, 10); // Assuming 10km distance
  const estimatedTime = getDeliveryTimeEstimate(10);

  if (bookingConfirmed) {
    return (
      <div className="min-h-screen bg-gray-50 py-12">
        <div className="max-w-2xl mx-auto px-4">
          <div className="bg-white rounded-xl shadow-lg p-8 text-center">
            <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-6">
              <Package2 className="h-8 w-8 text-green-600" />
            </div>
            <h1 className="text-3xl font-bold text-gray-900 mb-4">Booking Confirmed!</h1>
            <p className="text-gray-600 mb-6">Your parcel has been booked successfully.</p>
            
            <div className="bg-gray-50 rounded-lg p-6 mb-6">
              <h3 className="font-semibold text-gray-900 mb-2">Tracking ID</h3>
              <div className="text-2xl font-bold text-blue-600 mb-4">{trackingId}</div>
              <p className="text-sm text-gray-600">
                Save this tracking ID to monitor your delivery status
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
              <div className="text-left">
                <h4 className="font-medium text-gray-900 mb-2">Pickup Details</h4>
                <p className="text-sm text-gray-600">{formData.senderName}</p>
                <p className="text-sm text-gray-600">{formData.senderAddress}</p>
                <p className="text-sm text-gray-600">{formData.pickupDate} at {formData.pickupTime}</p>
              </div>
              <div className="text-left">
                <h4 className="font-medium text-gray-900 mb-2">Delivery Details</h4>
                <p className="text-sm text-gray-600">{formData.receiverName}</p>
                <p className="text-sm text-gray-600">{formData.receiverAddress}</p>
                <p className="text-sm text-gray-600">Expected: {estimatedTime}</p>
              </div>
            </div>

            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <button
                onClick={() => window.location.href = `/track?id=${trackingId}`}
                className="bg-blue-600 text-white px-6 py-3 rounded-lg font-medium hover:bg-blue-700 transition-colors"
              >
                Track Package
              </button>
              <button
                onClick={() => window.location.reload()}
                className="border border-gray-300 text-gray-700 px-6 py-3 rounded-lg font-medium hover:bg-gray-50 transition-colors"
              >
                Book Another
              </button>
            </div>
          </div>
        </div>
      </div>
    );
  }

  const steps = [
    { number: 1, title: 'Sender Details', icon: User },
    { number: 2, title: 'Receiver Details', icon: MapPin },
    { number: 3, title: 'Package Details', icon: Package2 },
    { number: 4, title: 'Schedule & Payment', icon: Calendar }
  ];

  return (
    <div className="min-h-screen bg-gray-50 py-12">
      <div className="max-w-4xl mx-auto px-4">
        {/* Progress Steps */}
        <div className="mb-8">
          <div className="flex items-center justify-between">
            {steps.map((step, index) => {
              const Icon = step.icon;
              const isActive = currentStep === step.number;
              const isCompleted = currentStep > step.number;
              
              return (
                <React.Fragment key={step.number}>
                  <div className="flex flex-col items-center">
                    <div className={`w-12 h-12 rounded-full flex items-center justify-center mb-2 ${
                      isCompleted 
                        ? 'bg-green-600 text-white' 
                        : isActive 
                          ? 'bg-blue-600 text-white' 
                          : 'bg-gray-300 text-gray-600'
                    }`}>
                      <Icon className="h-6 w-6" />
                    </div>
                    <span className={`text-sm font-medium ${
                      isActive ? 'text-blue-600' : 'text-gray-600'
                    }`}>
                      {step.title}
                    </span>
                  </div>
                  {index < steps.length - 1 && (
                    <div className={`flex-1 h-1 mx-4 rounded ${
                      currentStep > step.number ? 'bg-green-600' : 'bg-gray-300'
                    }`} />
                  )}
                </React.Fragment>
              );
            })}
          </div>
        </div>

        <div className="bg-white rounded-xl shadow-lg p-8">
          {/* Step 1: Sender Details */}
          {currentStep === 1 && (
            <div className="space-y-6">
              <h2 className="text-2xl font-bold text-gray-900 mb-2">Sender Details</h2>
              <p className="text-gray-600 mb-6">Please provide your pickup information</p>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Full Name *
                  </label>
                  <input
                    type="text"
                    value={formData.senderName}
                    onChange={(e) => handleInputChange('senderName', e.target.value)}
                    className={`w-full px-4 py-3 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent ${
                      errors.senderName ? 'border-red-500' : 'border-gray-300'
                    }`}
                    placeholder="Enter your full name"
                  />
                  {errors.senderName && (
                    <p className="text-red-500 text-sm mt-1">{errors.senderName}</p>
                  )}
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Phone Number *
                  </label>
                  <input
                    type="tel"
                    value={formData.senderPhone}
                    onChange={(e) => handleInputChange('senderPhone', e.target.value)}
                    className={`w-full px-4 py-3 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent ${
                      errors.senderPhone ? 'border-red-500' : 'border-gray-300'
                    }`}
                    placeholder="Enter 10-digit mobile number"
                  />
                  {errors.senderPhone && (
                    <p className="text-red-500 text-sm mt-1">{errors.senderPhone}</p>
                  )}
                </div>

                <div className="md:col-span-2">
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Email Address *
                  </label>
                  <input
                    type="email"
                    value={formData.senderEmail}
                    onChange={(e) => handleInputChange('senderEmail', e.target.value)}
                    className={`w-full px-4 py-3 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent ${
                      errors.senderEmail ? 'border-red-500' : 'border-gray-300'
                    }`}
                    placeholder="Enter your email address"
                  />
                  {errors.senderEmail && (
                    <p className="text-red-500 text-sm mt-1">{errors.senderEmail}</p>
                  )}
                </div>

                <div className="md:col-span-2">
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Pickup Address *
                  </label>
                  <textarea
                    value={formData.senderAddress}
                    onChange={(e) => handleInputChange('senderAddress', e.target.value)}
                    rows={3}
                    className={`w-full px-4 py-3 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent ${
                      errors.senderAddress ? 'border-red-500' : 'border-gray-300'
                    }`}
                    placeholder="Enter complete pickup address with landmark"
                  />
                  {errors.senderAddress && (
                    <p className="text-red-500 text-sm mt-1">{errors.senderAddress}</p>
                  )}
                </div>
              </div>
            </div>
          )}

          {/* Step 2: Receiver Details */}
          {currentStep === 2 && (
            <div className="space-y-6">
              <h2 className="text-2xl font-bold text-gray-900 mb-2">Receiver Details</h2>
              <p className="text-gray-600 mb-6">Where should we deliver your package?</p>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Receiver Name *
                  </label>
                  <input
                    type="text"
                    value={formData.receiverName}
                    onChange={(e) => handleInputChange('receiverName', e.target.value)}
                    className={`w-full px-4 py-3 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent ${
                      errors.receiverName ? 'border-red-500' : 'border-gray-300'
                    }`}
                    placeholder="Enter receiver's full name"
                  />
                  {errors.receiverName && (
                    <p className="text-red-500 text-sm mt-1">{errors.receiverName}</p>
                  )}
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Receiver Phone *
                  </label>
                  <input
                    type="tel"
                    value={formData.receiverPhone}
                    onChange={(e) => handleInputChange('receiverPhone', e.target.value)}
                    className={`w-full px-4 py-3 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent ${
                      errors.receiverPhone ? 'border-red-500' : 'border-gray-300'
                    }`}
                    placeholder="Enter receiver's mobile number"
                  />
                  {errors.receiverPhone && (
                    <p className="text-red-500 text-sm mt-1">{errors.receiverPhone}</p>
                  )}
                </div>

                <div className="md:col-span-2">
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Delivery Address *
                  </label>
                  <textarea
                    value={formData.receiverAddress}
                    onChange={(e) => handleInputChange('receiverAddress', e.target.value)}
                    rows={3}
                    className={`w-full px-4 py-3 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent ${
                      errors.receiverAddress ? 'border-red-500' : 'border-gray-300'
                    }`}
                    placeholder="Enter complete delivery address with landmark"
                  />
                  {errors.receiverAddress && (
                    <p className="text-red-500 text-sm mt-1">{errors.receiverAddress}</p>
                  )}
                </div>
              </div>
            </div>
          )}

          {/* Step 3: Package Details */}
          {currentStep === 3 && (
            <div className="space-y-6">
              <h2 className="text-2xl font-bold text-gray-900 mb-2">Package Details</h2>
              <p className="text-gray-600 mb-6">Tell us about your package</p>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Package Type *
                  </label>
                  <select
                    value={formData.packageType}
                    onChange={(e) => handleInputChange('packageType', e.target.value)}
                    className={`w-full px-4 py-3 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent ${
                      errors.packageType ? 'border-red-500' : 'border-gray-300'
                    }`}
                  >
                    <option value="">Select package type</option>
                    {PACKAGE_TYPES.map(type => (
                      <option key={type} value={type}>{type}</option>
                    ))}
                  </select>
                  {errors.packageType && (
                    <p className="text-red-500 text-sm mt-1">{errors.packageType}</p>
                  )}
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Weight (kg) *
                  </label>
                  <input
                    type="number"
                    step="0.1"
                    min="0.1"
                    value={formData.weight || ''}
                    onChange={(e) => handleInputChange('weight', parseFloat(e.target.value) || 0)}
                    className={`w-full px-4 py-3 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent ${
                      errors.weight ? 'border-red-500' : 'border-gray-300'
                    }`}
                    placeholder="Enter weight in kg"
                  />
                  {errors.weight && (
                    <p className="text-red-500 text-sm mt-1">{errors.weight}</p>
                  )}
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Length (cm) *
                  </label>
                  <input
                    type="number"
                    min="1"
                    value={formData.length || ''}
                    onChange={(e) => handleInputChange('length', parseFloat(e.target.value) || 0)}
                    className={`w-full px-4 py-3 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent ${
                      errors.length ? 'border-red-500' : 'border-gray-300'
                    }`}
                    placeholder="Length"
                  />
                  {errors.length && (
                    <p className="text-red-500 text-sm mt-1">{errors.length}</p>
                  )}
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Width (cm) *
                  </label>
                  <input
                    type="number"
                    min="1"
                    value={formData.width || ''}
                    onChange={(e) => handleInputChange('width', parseFloat(e.target.value) || 0)}
                    className={`w-full px-4 py-3 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent ${
                      errors.width ? 'border-red-500' : 'border-gray-300'
                    }`}
                    placeholder="Width"
                  />
                  {errors.width && (
                    <p className="text-red-500 text-sm mt-1">{errors.width}</p>
                  )}
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Height (cm) *
                  </label>
                  <input
                    type="number"
                    min="1"
                    value={formData.height || ''}
                    onChange={(e) => handleInputChange('height', parseFloat(e.target.value) || 0)}
                    className={`w-full px-4 py-3 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent ${
                      errors.height ? 'border-red-500' : 'border-gray-300'
                    }`}
                    placeholder="Height"
                  />
                  {errors.height && (
                    <p className="text-red-500 text-sm mt-1">{errors.height}</p>
                  )}
                </div>

                <div className="md:col-span-2">
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Package Description
                  </label>
                  <textarea
                    value={formData.description}
                    onChange={(e) => handleInputChange('description', e.target.value)}
                    rows={3}
                    className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    placeholder="Describe the contents (optional)"
                  />
                </div>
              </div>
            </div>
          )}

          {/* Step 4: Schedule & Payment */}
          {currentStep === 4 && (
            <div className="space-y-6">
              <h2 className="text-2xl font-bold text-gray-900 mb-2">Schedule & Payment</h2>
              <p className="text-gray-600 mb-6">When should we pick up your package?</p>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Pickup Date *
                  </label>
                  <input
                    type="date"
                    value={formData.pickupDate}
                    min={new Date().toISOString().split('T')[0]}
                    onChange={(e) => handleInputChange('pickupDate', e.target.value)}
                    className={`w-full px-4 py-3 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent ${
                      errors.pickupDate ? 'border-red-500' : 'border-gray-300'
                    }`}
                  />
                  {errors.pickupDate && (
                    <p className="text-red-500 text-sm mt-1">{errors.pickupDate}</p>
                  )}
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Pickup Time *
                  </label>
                  <select
                    value={formData.pickupTime}
                    onChange={(e) => handleInputChange('pickupTime', e.target.value)}
                    className={`w-full px-4 py-3 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent ${
                      errors.pickupTime ? 'border-red-500' : 'border-gray-300'
                    }`}
                  >
                    <option value="">Select time slot</option>
                    <option value="09:00-12:00">9:00 AM - 12:00 PM</option>
                    <option value="12:00-15:00">12:00 PM - 3:00 PM</option>
                    <option value="15:00-18:00">3:00 PM - 6:00 PM</option>
                    <option value="18:00-21:00">6:00 PM - 9:00 PM</option>
                  </select>
                  {errors.pickupTime && (
                    <p className="text-red-500 text-sm mt-1">{errors.pickupTime}</p>
                  )}
                </div>

                <div className="md:col-span-2">
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Delivery Instructions
                  </label>
                  <textarea
                    value={formData.deliveryInstructions}
                    onChange={(e) => handleInputChange('deliveryInstructions', e.target.value)}
                    rows={3}
                    className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    placeholder="Any special instructions for delivery (optional)"
                  />
                </div>
              </div>

              {/* Pricing Summary */}
              <div className="bg-gray-50 rounded-lg p-6 mt-8">
                <h3 className="text-lg font-semibold text-gray-900 mb-4">Delivery Summary</h3>
                <div className="space-y-3">
                  <div className="flex justify-between">
                    <span className="text-gray-600">Base delivery fee</span>
                    <span className="font-medium">₹30</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">Weight charges ({formData.weight}kg)</span>
                    <span className="font-medium">₹{(formData.weight * 5).toFixed(2)}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">Distance charges (10km)</span>
                    <span className="font-medium">₹20</span>
                  </div>
                  <div className="border-t pt-3 flex justify-between text-lg font-bold">
                    <span>Total Amount</span>
                    <span className="text-blue-600">₹{deliveryFee}</span>
                  </div>
                  <div className="text-sm text-gray-600">
                    <span className="font-medium">Estimated delivery time:</span> {estimatedTime}
                  </div>
                </div>
              </div>
            </div>
          )}

          {/* Navigation Buttons */}
          <div className="flex justify-between pt-8 border-t">
            <button
              onClick={() => setCurrentStep(prev => prev - 1)}
              disabled={currentStep === 1}
              className={`px-6 py-3 rounded-lg font-medium transition-colors ${
                currentStep === 1
                  ? 'bg-gray-100 text-gray-400 cursor-not-allowed'
                  : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
              }`}
            >
              Previous
            </button>

            {currentStep === 4 ? (
              <button
                onClick={handleSubmit}
                disabled={isSubmitting}
                className="bg-blue-600 text-white px-8 py-3 rounded-lg font-medium hover:bg-blue-700 transition-colors disabled:bg-blue-400 flex items-center space-x-2"
              >
                {isSubmitting ? (
                  <>
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                    <span>Processing...</span>
                  </>
                ) : (
                  <>
                    <CreditCard className="h-4 w-4" />
                    <span>Book & Pay ₹{deliveryFee}</span>
                  </>
                )}
              </button>
            ) : (
              <button
                onClick={handleNext}
                className="bg-blue-600 text-white px-6 py-3 rounded-lg font-medium hover:bg-blue-700 transition-colors flex items-center space-x-2"
              >
                <span>Next</span>
                <ArrowRight className="h-4 w-4" />
              </button>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default BookParcelPage;