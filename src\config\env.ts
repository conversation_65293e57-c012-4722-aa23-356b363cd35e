// Environment configuration
export const config = {
  // API Configuration
  API_BASE_URL: import.meta.env.VITE_API_BASE_URL || 'http://localhost:3001/api',
  
  // Google Maps API
  GOOGLE_MAPS_API_KEY: import.meta.env.VITE_GOOGLE_MAPS_API_KEY || '',
  
  // Firebase Configuration
  FIREBASE_CONFIG: {
    apiKey: import.meta.env.VITE_FIREBASE_API_KEY || '',
    authDomain: import.meta.env.VITE_FIREBASE_AUTH_DOMAIN || '',
    projectId: import.meta.env.VITE_FIREBASE_PROJECT_ID || '',
    storageBucket: import.meta.env.VITE_FIREBASE_STORAGE_BUCKET || '',
    messagingSenderId: import.meta.env.VITE_FIREBASE_MESSAGING_SENDER_ID || '',
    appId: import.meta.env.VITE_FIREBASE_APP_ID || '',
  },
  
  // Stripe Configuration
  STRIPE_PUBLISHABLE_KEY: import.meta.env.VITE_STRIPE_PUBLISHABLE_KEY || '',
  
  // App Configuration
  APP_NAME: 'QuickDash',
  APP_VERSION: '1.0.0',
  
  // Feature Flags
  FEATURES: {
    GOOGLE_MAPS: Boolean(import.meta.env.VITE_GOOGLE_MAPS_API_KEY),
    STRIPE_PAYMENTS: Boolean(import.meta.env.VITE_STRIPE_PUBLISHABLE_KEY),
    FIREBASE_AUTH: Boolean(import.meta.env.VITE_FIREBASE_API_KEY),
  },
  
  // Development
  IS_DEVELOPMENT: import.meta.env.DEV,
  IS_PRODUCTION: import.meta.env.PROD,
};

// Validation
export const validateConfig = () => {
  const requiredEnvVars = [
    'VITE_API_BASE_URL',
    'VITE_GOOGLE_MAPS_API_KEY',
    'VITE_FIREBASE_API_KEY',
    'VITE_STRIPE_PUBLISHABLE_KEY',
  ];

  const missingVars = requiredEnvVars.filter(
    varName => !import.meta.env[varName]
  );

  if (missingVars.length > 0 && config.IS_PRODUCTION) {
    console.warn(
      `Missing environment variables: ${missingVars.join(', ')}`
    );
  }

  return {
    isValid: missingVars.length === 0,
    missingVars,
  };
};
