import React from 'react';
import { Link } from 'react-router-dom';
import { 
  Package2, 
  Clock, 
  Shield, 
  Smartphone, 
  MapPin, 
  Star,
  Truck,
  Users,
  ArrowRight
} from 'lucide-react';
import GoogleMap from '../components/GoogleMap';

const HomePage: React.FC = () => {
  const features = [
    {
      icon: Clock,
      title: 'Fast Delivery',
      description: 'Same-day delivery within your city. Most parcels delivered within 2-4 hours.'
    },
    {
      icon: Shield,
      title: 'Secure & Safe',
      description: 'Your packages are insured and tracked end-to-end for complete peace of mind.'
    },
    {
      icon: MapPin,
      title: 'Hyperlocal',
      description: 'Specialized in local deliveries with deep knowledge of your area.'
    },
    {
      icon: Smartphone,
      title: 'Easy Tracking',
      description: 'Real-time tracking with SMS updates and mobile app notifications.'
    }
  ];

  const stats = [
    { number: '50K+', label: 'Deliveries Completed' },
    { number: '5K+', label: 'Happy Customers' },
    { number: '200+', label: 'Delivery Partners' },
    { number: '15', label: 'Cities Covered' }
  ];

  const testimonials = [
    {
      name: '<PERSON><PERSON>',
      rating: 5,
      comment: 'Super fast delivery! My documents reached on time for an important meeting.'
    },
    {
      name: '<PERSON><PERSON>',
      rating: 5,
      comment: 'Reliable service. I use QuickDash for all my business deliveries.'
    },
    {
      name: 'Anjali Patel',
      rating: 5,
      comment: 'Great tracking system. I always know where my package is!'
    }
  ];

  return (
    <div className="space-y-16">
      {/* Hero Section */}
      <section className="bg-gradient-to-br from-blue-600 via-blue-700 to-emerald-600 text-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-20">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
            <div className="space-y-8">
              <h1 className="text-4xl md:text-6xl font-bold leading-tight">
                Lightning Fast
                <span className="block text-yellow-300">Local Delivery</span>
              </h1>
              <p className="text-xl text-blue-100 leading-relaxed">
                Get your parcels delivered within hours, not days. QuickDash connects you with 
                local delivery partners for the fastest, most reliable service in your city.
              </p>
              <div className="flex flex-col sm:flex-row gap-4">
                <Link
                  to="/book"
                  className="bg-white text-blue-600 px-8 py-4 rounded-lg font-semibold hover:bg-gray-50 transition-colors flex items-center justify-center space-x-2 group"
                >
                  <Package2 className="h-5 w-5" />
                  <span>Book Now</span>
                  <ArrowRight className="h-4 w-4 group-hover:translate-x-1 transition-transform" />
                </Link>
                <Link
                  to="/track"
                  className="border-2 border-white text-white px-8 py-4 rounded-lg font-semibold hover:bg-white hover:text-blue-600 transition-colors text-center"
                >
                  Track Package
                </Link>
              </div>
            </div>
            <div className="relative">
              <div className="bg-white/10 backdrop-blur-sm rounded-2xl p-8">
                <div className="text-center">
                  <div className="text-6xl mb-4">📦</div>
                  <h3 className="text-2xl font-bold mb-4">Quick Booking</h3>
                  <p className="text-blue-100 mb-6">Book in under 2 minutes</p>
                  <div className="bg-white/20 rounded-lg p-4 space-y-3">
                    <div className="flex items-center space-x-3">
                      <div className="w-3 h-3 bg-green-400 rounded-full"></div>
                      <span className="text-sm">Pickup scheduled</span>
                    </div>
                    <div className="flex items-center space-x-3">
                      <div className="w-3 h-3 bg-yellow-400 rounded-full"></div>
                      <span className="text-sm">In transit</span>
                    </div>
                    <div className="flex items-center space-x-3">
                      <div className="w-3 h-3 bg-blue-400 rounded-full animate-pulse"></div>
                      <span className="text-sm">Out for delivery</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section className="py-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
              Why Choose QuickDash?
            </h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              We've revolutionized local delivery with technology, speed, and reliability
            </p>
          </div>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            {features.map((feature, index) => {
              const Icon = feature.icon;
              return (
                <div
                  key={index}
                  className="bg-white p-6 rounded-xl shadow-lg hover:shadow-xl transition-shadow border border-gray-100 group"
                >
                  <div className="bg-gradient-to-r from-blue-500 to-emerald-500 w-12 h-12 rounded-lg flex items-center justify-center mb-4 group-hover:scale-110 transition-transform">
                    <Icon className="h-6 w-6 text-white" />
                  </div>
                  <h3 className="text-xl font-semibold text-gray-900 mb-2">{feature.title}</h3>
                  <p className="text-gray-600">{feature.description}</p>
                </div>
              );
            })}
          </div>
        </div>
      </section>

      {/* Stats Section */}
      <section className="py-16 bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-2 lg:grid-cols-4 gap-8">
            {stats.map((stat, index) => (
              <div key={index} className="text-center">
                <div className="text-3xl md:text-4xl font-bold text-blue-600 mb-2">
                  {stat.number}
                </div>
                <div className="text-gray-600 font-medium">{stat.label}</div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Service Areas */}
      <section className="py-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
              Our Service Areas
            </h2>
            <p className="text-xl text-gray-600">
              Delivering across major neighborhoods in your city
            </p>
          </div>
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
            <GoogleMap
              center={{ lat: 28.6139, lng: 77.2090 }}
              markers={[
                { position: { lat: 28.6139, lng: 77.2090 }, title: 'Central Hub' },
                { position: { lat: 28.6340, lng: 77.2200 }, title: 'North Zone' },
                { position: { lat: 28.5900, lng: 77.1900 }, title: 'South Zone' }
              ]}
            />
            <div className="space-y-6">
              <h3 className="text-2xl font-bold text-gray-900">Fast Coverage Across The City</h3>
              <p className="text-gray-600 leading-relaxed">
                Our delivery network spans across all major areas, ensuring quick and 
                efficient service no matter where you are located.
              </p>
              <div className="grid grid-cols-2 gap-4">
                <div className="flex items-center space-x-2">
                  <MapPin className="h-5 w-5 text-blue-500" />
                  <span className="text-gray-700">Downtown</span>
                </div>
                <div className="flex items-center space-x-2">
                  <MapPin className="h-5 w-5 text-emerald-500" />
                  <span className="text-gray-700">Business District</span>
                </div>
                <div className="flex items-center space-x-2">
                  <MapPin className="h-5 w-5 text-orange-500" />
                  <span className="text-gray-700">Residential Areas</span>
                </div>
                <div className="flex items-center space-x-2">
                  <MapPin className="h-5 w-5 text-purple-500" />
                  <span className="text-gray-700">Industrial Zone</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Testimonials */}
      <section className="py-16 bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
              What Our Customers Say
            </h2>
          </div>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {testimonials.map((testimonial, index) => (
              <div key={index} className="bg-white p-6 rounded-xl shadow-lg">
                <div className="flex items-center mb-4">
                  {[...Array(testimonial.rating)].map((_, i) => (
                    <Star key={i} className="h-5 w-5 text-yellow-400 fill-current" />
                  ))}
                </div>
                <p className="text-gray-600 mb-4">"{testimonial.comment}"</p>
                <p className="font-semibold text-gray-900">- {testimonial.name}</p>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-16 bg-gradient-to-r from-blue-600 to-emerald-600 text-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h2 className="text-3xl md:text-4xl font-bold mb-4">
            Ready to Send Your Package?
          </h2>
          <p className="text-xl text-blue-100 mb-8 max-w-2xl mx-auto">
            Book your delivery in minutes and track it in real-time. 
            Join thousands of satisfied customers.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Link
              to="/book"
              className="bg-white text-blue-600 px-8 py-4 rounded-lg font-semibold hover:bg-gray-50 transition-colors inline-flex items-center justify-center space-x-2"
            >
              <Package2 className="h-5 w-5" />
              <span>Book Delivery</span>
            </Link>
            <Link
              to="/partner"
              className="border-2 border-white text-white px-8 py-4 rounded-lg font-semibold hover:bg-white hover:text-blue-600 transition-colors inline-flex items-center justify-center space-x-2"
            >
              <Users className="h-5 w-5" />
              <span>Become Partner</span>
            </Link>
          </div>
        </div>
      </section>
    </div>
  );
};

export default HomePage;