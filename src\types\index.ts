export interface User {
  id: string;
  name: string;
  email: string;
  phone: string;
  role: 'customer' | 'partner' | 'admin';
  createdAt: Date;
}

export interface Parcel {
  id: string;
  trackingId: string;
  senderId: string;
  senderName: string;
  senderPhone: string;
  senderAddress: string;
  receiverName: string;
  receiverPhone: string;
  receiverAddress: string;
  packageType: string;
  weight: number;
  dimensions: {
    length: number;
    width: number;
    height: number;
  };
  pickupDate: Date;
  deliveryDate?: Date;
  status: 'pending' | 'picked_up' | 'in_transit' | 'delivered' | 'cancelled';
  amount: number;
  paymentStatus: 'pending' | 'paid' | 'refunded';
  partnerId?: string;
  createdAt: Date;
}

export interface DeliveryPartner {
  id: string;
  name: string;
  email: string;
  phone: string;
  vehicleType: string;
  vehicleNumber: string;
  licenseNumber: string;
  aadharNumber: string;
  bankAccount: string;
  ifscCode: string;
  address: string;
  city: string;
  pincode: string;
  status: 'pending' | 'approved' | 'rejected' | 'suspended';
  documentsUploaded: boolean;
  createdAt: Date;
}

export interface Location {
  lat: number;
  lng: number;
  address: string;
}

export interface ServiceArea {
  id: string;
  name: string;
  boundaries: Location[];
  isActive: boolean;
}