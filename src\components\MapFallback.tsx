import React from 'react';
import { MapPin } from 'lucide-react';

interface MapFallbackProps {
  center: { lat: number; lng: number };
  markers?: Array<{
    position: { lat: number; lng: number };
    title?: string;
    info?: string;
  }>;
  className?: string;
  showTraffic?: boolean;
}

const MapFallback: React.FC<MapFallbackProps> = ({ 
  center, 
  markers = [], 
  className = "w-full h-96",
  showTraffic = false 
}) => {
  return (
    <div className={className}>
      <div className="w-full h-full bg-gradient-to-br from-blue-50 to-emerald-50 rounded-lg flex items-center justify-center border-2 border-dashed border-gray-300">
        <div className="text-center p-8">
          <div className="flex justify-center mb-4">
            <div className="bg-gradient-to-r from-blue-600 to-emerald-600 p-3 rounded-full">
              <MapPin className="h-8 w-8 text-white" />
            </div>
          </div>
          
          <h3 className="text-lg font-semibold text-gray-900 mb-2">
            Interactive Map
          </h3>
          
          <p className="text-sm text-gray-600 mb-4">
            Map integration ready for Google Maps API
          </p>
          
          <div className="bg-white rounded-lg p-4 shadow-sm">
            <div className="text-xs text-gray-500 space-y-1">
              <p><strong>Center:</strong> {center.lat.toFixed(4)}, {center.lng.toFixed(4)}</p>
              {markers.length > 0 && (
                <p><strong>Markers:</strong> {markers.length} location(s)</p>
              )}
              {showTraffic && (
                <p><strong>Traffic:</strong> Enabled</p>
              )}
            </div>
          </div>
          
          {markers.length > 0 && (
            <div className="mt-4 space-y-2">
              <h4 className="text-sm font-medium text-gray-700">Locations:</h4>
              <div className="max-h-32 overflow-y-auto space-y-1">
                {markers.map((marker, index) => (
                  <div key={index} className="bg-white rounded px-3 py-2 text-xs text-gray-600 shadow-sm">
                    <div className="font-medium">{marker.title || `Location ${index + 1}`}</div>
                    <div className="text-gray-500">
                      {marker.position.lat.toFixed(4)}, {marker.position.lng.toFixed(4)}
                    </div>
                    {marker.info && (
                      <div className="text-gray-400 mt-1">{marker.info}</div>
                    )}
                  </div>
                ))}
              </div>
            </div>
          )}
          
          <div className="mt-4 text-xs text-gray-400">
            Configure VITE_GOOGLE_MAPS_API_KEY to enable real maps
          </div>
        </div>
      </div>
    </div>
  );
};

export default MapFallback;
