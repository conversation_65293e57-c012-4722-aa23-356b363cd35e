import React from 'react';
import { Smartphone, Download, Star, Shield, Clock, Bell, MapPin, Users } from 'lucide-react';

const MobileAppPage: React.FC = () => {
  const features = [
    {
      icon: Clock,
      title: 'Real-time Tracking',
      description: 'Track your packages in real-time with live GPS updates and delivery notifications.'
    },
    {
      icon: Bell,
      title: 'Smart Notifications',
      description: 'Get instant updates about pickup, transit, and delivery status via push notifications.'
    },
    {
      icon: MapPin,
      title: 'GPS Integration',
      description: 'Find nearby pickup points and track your delivery partner on the map.'
    },
    {
      icon: Shield,
      title: 'Secure Payments',
      description: 'Pay securely with multiple payment options including UPI, cards, and wallets.'
    },
    {
      icon: Users,
      title: 'Easy Booking',
      description: 'Book deliveries in seconds with saved addresses and preferences.'
    },
    {
      icon: Star,
      title: 'Rate & Review',
      description: 'Rate your delivery experience and help us improve our service quality.'
    }
  ];

  const screenshots = [
    { title: 'Home Screen', image: '📱' },
    { title: 'Book Delivery', image: '📦' },
    { title: 'Track Package', image: '🗺️' },
    { title: 'Live Tracking', image: '🚚' }
  ];

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Hero Section */}
      <section className="bg-gradient-to-br from-blue-600 via-blue-700 to-emerald-600 text-white py-20">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
            <div className="space-y-8">
              <div className="space-y-4">
                <h1 className="text-4xl md:text-6xl font-bold leading-tight">
                  QuickDash
                  <span className="block text-yellow-300">Mobile App</span>
                </h1>
                <p className="text-xl text-blue-100 leading-relaxed">
                  Experience the fastest way to send and track packages with our 
                  feature-rich mobile app. Available for iOS and Android.
                </p>
              </div>

              <div className="flex items-center space-x-4">
                <Star className="h-6 w-6 text-yellow-400 fill-current" />
                <div>
                  <div className="flex items-center space-x-1">
                    {[...Array(5)].map((_, i) => (
                      <Star key={i} className="h-4 w-4 text-yellow-400 fill-current" />
                    ))}
                    <span className="text-white font-medium ml-2">4.8/5</span>
                  </div>
                  <p className="text-blue-100 text-sm">Based on 10,000+ reviews</p>
                </div>
              </div>

              <div className="flex flex-col sm:flex-row gap-4">
                <a
                  href="#download"
                  className="bg-white text-blue-600 px-8 py-4 rounded-lg font-semibold hover:bg-gray-50 transition-colors flex items-center justify-center space-x-2 group"
                >
                  <Download className="h-5 w-5" />
                  <span>Download Now</span>
                </a>
                <a
                  href="#features"
                  className="border-2 border-white text-white px-8 py-4 rounded-lg font-semibold hover:bg-white hover:text-blue-600 transition-colors text-center"
                >
                  View Features
                </a>
              </div>
            </div>

            <div className="relative">
              <div className="bg-white/10 backdrop-blur-sm rounded-3xl p-8 text-center">
                <div className="text-8xl mb-6">📱</div>
                <h3 className="text-2xl font-bold mb-4">Download Today</h3>
                <p className="text-blue-100 mb-6">Join 50,000+ users already using QuickDash</p>
                
                <div className="grid grid-cols-2 gap-4 text-sm">
                  <div className="bg-white/20 rounded-lg p-3">
                    <div className="font-bold text-lg">50K+</div>
                    <div className="text-blue-100">Downloads</div>
                  </div>
                  <div className="bg-white/20 rounded-lg p-3">
                    <div className="font-bold text-lg">4.8★</div>
                    <div className="text-blue-100">App Rating</div>
                  </div>
                  <div className="bg-white/20 rounded-lg p-3">
                    <div className="font-bold text-lg">24/7</div>
                    <div className="text-blue-100">Support</div>
                  </div>
                  <div className="bg-white/20 rounded-lg p-3">
                    <div className="font-bold text-lg">Free</div>
                    <div className="text-blue-100">to Download</div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* App Screenshots */}
      <section className="py-16 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
              Beautiful & Intuitive Design
            </h2>
            <p className="text-xl text-gray-600">
              Experience seamless package delivery with our user-friendly interface
            </p>
          </div>

          <div className="grid grid-cols-2 md:grid-cols-4 gap-8">
            {screenshots.map((screenshot, index) => (
              <div key={index} className="group">
                <div className="bg-gradient-to-br from-blue-500 to-emerald-500 rounded-2xl p-6 text-center text-white group-hover:scale-105 transition-transform">
                  <div className="text-6xl mb-4">{screenshot.image}</div>
                  <h3 className="font-semibold">{screenshot.title}</h3>
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section id="features" className="py-16 bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
              Powerful Features
            </h2>
            <p className="text-xl text-gray-600">
              Everything you need for hassle-free package delivery
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {features.map((feature, index) => {
              const Icon = feature.icon;
              return (
                <div
                  key={index}
                  className="bg-white p-8 rounded-xl shadow-lg hover:shadow-xl transition-shadow border border-gray-100 group"
                >
                  <div className="bg-gradient-to-r from-blue-500 to-emerald-500 w-12 h-12 rounded-lg flex items-center justify-center mb-6 group-hover:scale-110 transition-transform">
                    <Icon className="h-6 w-6 text-white" />
                  </div>
                  <h3 className="text-xl font-semibold text-gray-900 mb-3">{feature.title}</h3>
                  <p className="text-gray-600 leading-relaxed">{feature.description}</p>
                </div>
              );
            })}
          </div>
        </div>
      </section>

      {/* App Stats */}
      <section className="py-16 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
              Trusted by Thousands
            </h2>
          </div>

          <div className="grid grid-cols-2 lg:grid-cols-4 gap-8">
            <div className="text-center">
              <div className="text-4xl font-bold text-blue-600 mb-2">50K+</div>
              <div className="text-gray-600">App Downloads</div>
            </div>
            <div className="text-center">
              <div className="text-4xl font-bold text-emerald-600 mb-2">4.8★</div>
              <div className="text-gray-600">Average Rating</div>
            </div>
            <div className="text-center">
              <div className="text-4xl font-bold text-orange-600 mb-2">100K+</div>
              <div className="text-gray-600">Deliveries Completed</div>
            </div>
            <div className="text-center">
              <div className="text-4xl font-bold text-purple-600 mb-2">24/7</div>
              <div className="text-gray-600">Customer Support</div>
            </div>
          </div>
        </div>
      </section>

      {/* Download Section */}
      <section id="download" className="py-16 bg-gradient-to-r from-blue-600 to-emerald-600 text-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center space-y-8">
            <div>
              <h2 className="text-3xl md:text-4xl font-bold mb-4">
                Download QuickDash Today
              </h2>
              <p className="text-xl text-blue-100 max-w-2xl mx-auto">
                Get the app and start sending packages faster than ever before. 
                Available for free on both iOS and Android platforms.
              </p>
            </div>

            {/* QR Code */}
            <div className="bg-white rounded-2xl p-8 inline-block">
              <div className="text-6xl mb-4">📱</div>
              <p className="text-gray-600 font-medium">Scan to Download</p>
              <div className="mt-4 bg-gray-100 w-32 h-32 mx-auto rounded-lg flex items-center justify-center">
                <div className="text-4xl">QR</div>
              </div>
            </div>

            {/* Download Buttons */}
            <div className="flex flex-col sm:flex-row gap-4 justify-center items-center">
              <div className="flex items-center bg-black text-white px-6 py-3 rounded-lg hover:bg-gray-800 transition-colors cursor-pointer">
                <div className="mr-3">
                  <div className="text-2xl">🍎</div>
                </div>
                <div className="text-left">
                  <div className="text-xs">Download on the</div>
                  <div className="text-lg font-semibold">App Store</div>
                </div>
              </div>

              <div className="flex items-center bg-black text-white px-6 py-3 rounded-lg hover:bg-gray-800 transition-colors cursor-pointer">
                <div className="mr-3">
                  <div className="text-2xl">🤖</div>
                </div>
                <div className="text-left">
                  <div className="text-xs">Get it on</div>
                  <div className="text-lg font-semibold">Google Play</div>
                </div>
              </div>
            </div>

            <div className="text-center text-blue-100">
              <p>🔒 Your data is secure and encrypted</p>
              <p>📞 24/7 customer support available</p>
            </div>
          </div>
        </div>
      </section>

      {/* FAQ Section */}
      <section className="py-16 bg-gray-50">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
              Frequently Asked Questions
            </h2>
          </div>

          <div className="space-y-6">
            <div className="bg-white rounded-lg p-6 shadow-sm">
              <h3 className="font-semibold text-gray-900 mb-2">Is the app free to download?</h3>
              <p className="text-gray-600">Yes, QuickDash app is completely free to download and use. You only pay for the delivery services you book.</p>
            </div>

            <div className="bg-white rounded-lg p-6 shadow-sm">
              <h3 className="font-semibold text-gray-900 mb-2">Which devices are supported?</h3>
              <p className="text-gray-600">Our app works on iOS 12+ and Android 8+ devices. It's optimized for both phones and tablets.</p>
            </div>

            <div className="bg-white rounded-lg p-6 shadow-sm">
              <h3 className="font-semibold text-gray-900 mb-2">Can I track packages in real-time?</h3>
              <p className="text-gray-600">Yes, you can track your packages in real-time with live GPS updates and receive push notifications at every step.</p>
            </div>

            <div className="bg-white rounded-lg p-6 shadow-sm">
              <h3 className="font-semibold text-gray-900 mb-2">What payment methods are accepted?</h3>
              <p className="text-gray-600">We accept UPI, credit/debit cards, net banking, and popular mobile wallets like Paytm, PhonePe, and Google Pay.</p>
            </div>
          </div>
        </div>
      </section>
    </div>
  );
};

export default MobileAppPage;