import { createSlice, createAsyncThunk, PayloadAction } from '@reduxjs/toolkit';
import { Parcel } from '../../types';
import { parcelService } from '../../services/parcelService';

interface ParcelState {
  items: Parcel[];
  currentParcel: Parcel | null;
  isLoading: boolean;
  error: string | null;
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
  filters: {
    status?: string;
    dateRange?: { start: Date; end: Date };
    searchTerm?: string;
  };
}

const initialState: ParcelState = {
  items: [],
  currentParcel: null,
  isLoading: false,
  error: null,
  pagination: {
    page: 1,
    limit: 10,
    total: 0,
    totalPages: 0,
  },
  filters: {},
};

// Async thunks
export const createParcel = createAsyncThunk(
  'parcels/create',
  async (parcelData: Omit<Parcel, 'id' | 'trackingId' | 'createdAt'>, { rejectWithValue }) => {
    try {
      const parcel = await parcelService.createParcel(parcelData);
      return parcel;
    } catch (error: any) {
      return rejectWithValue(error.message || 'Failed to create parcel');
    }
  }
);

export const fetchParcels = createAsyncThunk(
  'parcels/fetchAll',
  async (params: {
    page?: number;
    limit?: number;
    status?: string;
    searchTerm?: string;
  } = {}, { rejectWithValue }) => {
    try {
      const response = await parcelService.getParcels(params);
      return response;
    } catch (error: any) {
      return rejectWithValue(error.message || 'Failed to fetch parcels');
    }
  }
);

export const fetchParcelByTrackingId = createAsyncThunk(
  'parcels/fetchByTrackingId',
  async (trackingId: string, { rejectWithValue }) => {
    try {
      const parcel = await parcelService.getParcelByTrackingId(trackingId);
      return parcel;
    } catch (error: any) {
      return rejectWithValue(error.message || 'Parcel not found');
    }
  }
);

export const updateParcelStatus = createAsyncThunk(
  'parcels/updateStatus',
  async ({ id, status }: { id: string; status: string }, { rejectWithValue }) => {
    try {
      const parcel = await parcelService.updateParcelStatus(id, status);
      return parcel;
    } catch (error: any) {
      return rejectWithValue(error.message || 'Failed to update parcel status');
    }
  }
);

export const assignPartnerToParcel = createAsyncThunk(
  'parcels/assignPartner',
  async ({ parcelId, partnerId }: { parcelId: string; partnerId: string }, { rejectWithValue }) => {
    try {
      const parcel = await parcelService.assignPartner(parcelId, partnerId);
      return parcel;
    } catch (error: any) {
      return rejectWithValue(error.message || 'Failed to assign partner');
    }
  }
);

const parcelSlice = createSlice({
  name: 'parcels',
  initialState,
  reducers: {
    clearError: (state) => {
      state.error = null;
    },
    setCurrentParcel: (state, action: PayloadAction<Parcel | null>) => {
      state.currentParcel = action.payload;
    },
    updateFilters: (state, action: PayloadAction<Partial<ParcelState['filters']>>) => {
      state.filters = { ...state.filters, ...action.payload };
    },
    clearFilters: (state) => {
      state.filters = {};
    },
    setPagination: (state, action: PayloadAction<Partial<ParcelState['pagination']>>) => {
      state.pagination = { ...state.pagination, ...action.payload };
    },
  },
  extraReducers: (builder) => {
    builder
      // Create parcel
      .addCase(createParcel.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(createParcel.fulfilled, (state, action) => {
        state.isLoading = false;
        state.items.unshift(action.payload);
        state.currentParcel = action.payload;
      })
      .addCase(createParcel.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
      })
      // Fetch parcels
      .addCase(fetchParcels.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(fetchParcels.fulfilled, (state, action) => {
        state.isLoading = false;
        state.items = action.payload.parcels;
        state.pagination = action.payload.pagination;
      })
      .addCase(fetchParcels.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
      })
      // Fetch parcel by tracking ID
      .addCase(fetchParcelByTrackingId.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(fetchParcelByTrackingId.fulfilled, (state, action) => {
        state.isLoading = false;
        state.currentParcel = action.payload;
      })
      .addCase(fetchParcelByTrackingId.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
        state.currentParcel = null;
      })
      // Update parcel status
      .addCase(updateParcelStatus.fulfilled, (state, action) => {
        const index = state.items.findIndex(item => item.id === action.payload.id);
        if (index !== -1) {
          state.items[index] = action.payload;
        }
        if (state.currentParcel?.id === action.payload.id) {
          state.currentParcel = action.payload;
        }
      })
      // Assign partner
      .addCase(assignPartnerToParcel.fulfilled, (state, action) => {
        const index = state.items.findIndex(item => item.id === action.payload.id);
        if (index !== -1) {
          state.items[index] = action.payload;
        }
        if (state.currentParcel?.id === action.payload.id) {
          state.currentParcel = action.payload;
        }
      });
  },
});

export const {
  clearError,
  setCurrentParcel,
  updateFilters,
  clearFilters,
  setPagination,
} = parcelSlice.actions;

export default parcelSlice.reducer;
