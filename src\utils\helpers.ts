export function generateTrackingId(): string {
  const prefix = 'QD';
  const timestamp = Date.now().toString().slice(-6);
  const random = Math.random().toString(36).substring(2, 6).toUpperCase();
  return `${prefix}${timestamp}${random}`;
}

export function formatCurrency(amount: number): string {
  return new Intl.NumberFormat('en-IN', {
    style: 'currency',
    currency: 'INR'
  }).format(amount);
}

export function formatDate(date: Date): string {
  return new Intl.DateTimeFormat('en-IN', {
    year: 'numeric',
    month: 'short',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
  }).format(date);
}

export function calculateDeliveryFee(weight: number, distance: number): number {
  const baseFee = 30;
  const weightFee = weight * 5;
  const distanceFee = distance * 2;
  return Math.max(baseFee + weightFee + distanceFee, 50);
}

export function getDeliveryTimeEstimate(distance: number): string {
  if (distance <= 5) return '30-60 minutes';
  if (distance <= 10) return '1-2 hours';
  if (distance <= 20) return '2-4 hours';
  return 'Same day delivery';
}

export function validatePhoneNumber(phone: string): boolean {
  const phoneRegex = /^[6-9]\d{9}$/;
  return phoneRegex.test(phone);
}

export function validateEmail(email: string): boolean {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
}