import { configureStore } from '@reduxjs/toolkit';
import authSlice, {
  loginUser,
  registerUser,
  logoutUser,
  getCurrentUser,
  clearError,
  setUser,
  clearAuth,
} from '../slices/authSlice';
import { authService } from '../../services/authService';

// Mock the auth service
jest.mock('../../services/authService');
const mockedAuthService = authService as jest.Mocked<typeof authService>;

type RootState = {
  auth: ReturnType<typeof authSlice>;
};

describe('authSlice', () => {
  let store: ReturnType<typeof configureStore<RootState>>;

  beforeEach(() => {
    store = configureStore({
      reducer: {
        auth: authSlice,
      },
    });
    jest.clearAllMocks();
    localStorage.clear();
  });

  describe('initial state', () => {
    it('should have correct initial state', () => {
      const state = store.getState().auth;
      expect(state).toEqual({
        user: null,
        isAuthenticated: false,
        isLoading: false,
        error: null,
        token: null,
      });
    });

    it('should load token from localStorage', () => {
      localStorage.setItem('token', 'test-token');
      
      const newStore = configureStore({
        reducer: {
          auth: authSlice,
        },
      });
      
      const state = newStore.getState().auth;
      expect(state.token).toBe('test-token');
    });
  });

  describe('synchronous actions', () => {
    it('should clear error', () => {
      // First set an error
      store.dispatch({ type: 'auth/loginUser/rejected', payload: 'Test error' });
      expect(store.getState().auth.error).toBe('Test error');

      // Then clear it
      store.dispatch(clearError());
      expect(store.getState().auth.error).toBeNull();
    });

    it('should set user', () => {
      const user = {
        id: '1',
        name: 'Test User',
        email: '<EMAIL>',
        phone: '9876543210',
        role: 'customer' as const,
        createdAt: new Date(),
      };

      store.dispatch(setUser(user));
      const state = store.getState().auth;
      
      expect(state.user).toEqual(user);
      expect(state.isAuthenticated).toBe(true);
    });

    it('should clear auth', () => {
      // First set some auth data
      const user = {
        id: '1',
        name: 'Test User',
        email: '<EMAIL>',
        phone: '9876543210',
        role: 'customer' as const,
        createdAt: new Date(),
      };
      
      store.dispatch(setUser(user));
      localStorage.setItem('token', 'test-token');

      // Then clear it
      store.dispatch(clearAuth());
      const state = store.getState().auth;
      
      expect(state.user).toBeNull();
      expect(state.isAuthenticated).toBe(false);
      expect(state.token).toBeNull();
      expect(localStorage.getItem('token')).toBeNull();
    });
  });

  describe('loginUser async thunk', () => {
    it('should handle successful login', async () => {
      const mockResponse = {
        user: {
          id: '1',
          name: 'Test User',
          email: '<EMAIL>',
          phone: '9876543210',
          role: 'customer' as const,
          createdAt: new Date(),
        },
        token: 'test-token',
      };

      mockedAuthService.login.mockResolvedValue(mockResponse);

      const credentials = { email: '<EMAIL>', password: 'password' };
      await store.dispatch(loginUser(credentials));

      const state = store.getState().auth;
      expect(state.isLoading).toBe(false);
      expect(state.user).toEqual(mockResponse.user);
      expect(state.token).toBe('test-token');
      expect(state.isAuthenticated).toBe(true);
      expect(state.error).toBeNull();
      expect(localStorage.getItem('token')).toBe('test-token');
    });

    it('should handle login failure', async () => {
      const errorMessage = 'Invalid credentials';
      mockedAuthService.login.mockRejectedValue(new Error(errorMessage));

      const credentials = { email: '<EMAIL>', password: 'wrong-password' };
      await store.dispatch(loginUser(credentials));

      const state = store.getState().auth;
      expect(state.isLoading).toBe(false);
      expect(state.user).toBeNull();
      expect(state.token).toBeNull();
      expect(state.isAuthenticated).toBe(false);
      expect(state.error).toBe(errorMessage);
    });

    it('should set loading state during login', () => {
      mockedAuthService.login.mockImplementation(
        () => new Promise(resolve => setTimeout(resolve, 100))
      );

      const credentials = { email: '<EMAIL>', password: 'password' };
      store.dispatch(loginUser(credentials));

      const state = store.getState().auth;
      expect(state.isLoading).toBe(true);
      expect(state.error).toBeNull();
    });
  });

  describe('registerUser async thunk', () => {
    it('should handle successful registration', async () => {
      const mockResponse = {
        user: {
          id: '1',
          name: 'New User',
          email: '<EMAIL>',
          phone: '9876543210',
          role: 'customer' as const,
          createdAt: new Date(),
        },
        token: 'new-token',
      };

      mockedAuthService.register.mockResolvedValue(mockResponse);

      const userData = {
        name: 'New User',
        email: '<EMAIL>',
        password: 'password',
        phone: '9876543210',
        role: 'customer' as const,
      };

      await store.dispatch(registerUser(userData));

      const state = store.getState().auth;
      expect(state.isLoading).toBe(false);
      expect(state.user).toEqual(mockResponse.user);
      expect(state.token).toBe('new-token');
      expect(state.isAuthenticated).toBe(true);
      expect(state.error).toBeNull();
    });
  });

  describe('logoutUser async thunk', () => {
    it('should handle successful logout', async () => {
      // First login
      const user = {
        id: '1',
        name: 'Test User',
        email: '<EMAIL>',
        phone: '9876543210',
        role: 'customer' as const,
        createdAt: new Date(),
      };
      
      store.dispatch(setUser(user));
      localStorage.setItem('token', 'test-token');

      mockedAuthService.logout.mockResolvedValue();

      await store.dispatch(logoutUser());

      const state = store.getState().auth;
      expect(state.user).toBeNull();
      expect(state.token).toBeNull();
      expect(state.isAuthenticated).toBe(false);
      expect(state.isLoading).toBe(false);
      expect(state.error).toBeNull();
      expect(localStorage.getItem('token')).toBeNull();
    });
  });

  describe('getCurrentUser async thunk', () => {
    it('should handle successful user fetch', async () => {
      const mockUser = {
        id: '1',
        name: 'Current User',
        email: '<EMAIL>',
        phone: '9876543210',
        role: 'customer' as const,
        createdAt: new Date(),
      };

      localStorage.setItem('token', 'valid-token');
      mockedAuthService.getCurrentUser.mockResolvedValue(mockUser);

      await store.dispatch(getCurrentUser());

      const state = store.getState().auth;
      expect(state.isLoading).toBe(false);
      expect(state.user).toEqual(mockUser);
      expect(state.isAuthenticated).toBe(true);
      expect(state.error).toBeNull();
    });

    it('should handle failure when no token', async () => {
      await store.dispatch(getCurrentUser());

      const state = store.getState().auth;
      expect(state.isLoading).toBe(false);
      expect(state.user).toBeNull();
      expect(state.isAuthenticated).toBe(false);
      expect(state.error).toBe('No token found');
    });
  });
});
