import React from 'react';

interface SkeletonProps {
  className?: string;
  width?: string | number;
  height?: string | number;
  rounded?: boolean;
}

const Skeleton: React.FC<SkeletonProps> = ({ 
  className = '', 
  width, 
  height, 
  rounded = false 
}) => {
  const style: React.CSSProperties = {};
  if (width) style.width = typeof width === 'number' ? `${width}px` : width;
  if (height) style.height = typeof height === 'number' ? `${height}px` : height;

  return (
    <div
      className={`animate-pulse bg-gray-200 ${rounded ? 'rounded-full' : 'rounded'} ${className}`}
      style={style}
    />
  );
};

// Card Skeleton
export const CardSkeleton: React.FC<{ className?: string }> = ({ className = '' }) => (
  <div className={`bg-white rounded-lg shadow-sm p-6 ${className}`}>
    <div className="animate-pulse">
      <div className="flex items-center space-x-4 mb-4">
        <Skeleton width={40} height={40} rounded />
        <div className="flex-1">
          <Skeleton height={20} className="mb-2" />
          <Skeleton height={16} width="60%" />
        </div>
      </div>
      <Skeleton height={16} className="mb-2" />
      <Skeleton height={16} width="80%" className="mb-4" />
      <div className="flex space-x-2">
        <Skeleton height={32} width={80} />
        <Skeleton height={32} width={80} />
      </div>
    </div>
  </div>
);

// Table Skeleton
export const TableSkeleton: React.FC<{ 
  rows?: number; 
  columns?: number; 
  className?: string;
}> = ({ rows = 5, columns = 4, className = '' }) => (
  <div className={`bg-white rounded-lg shadow-sm overflow-hidden ${className}`}>
    <div className="animate-pulse">
      {/* Header */}
      <div className="bg-gray-50 px-6 py-4 border-b">
        <div className="grid gap-4" style={{ gridTemplateColumns: `repeat(${columns}, 1fr)` }}>
          {Array.from({ length: columns }).map((_, i) => (
            <Skeleton key={i} height={20} />
          ))}
        </div>
      </div>
      
      {/* Rows */}
      {Array.from({ length: rows }).map((_, rowIndex) => (
        <div key={rowIndex} className="px-6 py-4 border-b border-gray-100">
          <div className="grid gap-4" style={{ gridTemplateColumns: `repeat(${columns}, 1fr)` }}>
            {Array.from({ length: columns }).map((_, colIndex) => (
              <Skeleton key={colIndex} height={16} />
            ))}
          </div>
        </div>
      ))}
    </div>
  </div>
);

// List Skeleton
export const ListSkeleton: React.FC<{ 
  items?: number; 
  className?: string;
}> = ({ items = 5, className = '' }) => (
  <div className={`space-y-4 ${className}`}>
    {Array.from({ length: items }).map((_, index) => (
      <div key={index} className="bg-white rounded-lg shadow-sm p-4">
        <div className="animate-pulse flex items-center space-x-4">
          <Skeleton width={48} height={48} rounded />
          <div className="flex-1">
            <Skeleton height={20} className="mb-2" />
            <Skeleton height={16} width="70%" className="mb-1" />
            <Skeleton height={14} width="50%" />
          </div>
          <Skeleton width={80} height={32} />
        </div>
      </div>
    ))}
  </div>
);

// Form Skeleton
export const FormSkeleton: React.FC<{ 
  fields?: number; 
  className?: string;
}> = ({ fields = 6, className = '' }) => (
  <div className={`bg-white rounded-lg shadow-sm p-6 ${className}`}>
    <div className="animate-pulse space-y-6">
      <Skeleton height={32} width="40%" className="mb-6" />
      
      {Array.from({ length: fields }).map((_, index) => (
        <div key={index}>
          <Skeleton height={16} width="30%" className="mb-2" />
          <Skeleton height={40} />
        </div>
      ))}
      
      <div className="flex space-x-4 pt-4">
        <Skeleton height={40} width={120} />
        <Skeleton height={40} width={100} />
      </div>
    </div>
  </div>
);

// Stats Skeleton
export const StatsSkeleton: React.FC<{ 
  cards?: number; 
  className?: string;
}> = ({ cards = 4, className = '' }) => (
  <div className={`grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 ${className}`}>
    {Array.from({ length: cards }).map((_, index) => (
      <div key={index} className="bg-white rounded-lg shadow-sm p-6">
        <div className="animate-pulse">
          <div className="flex items-center justify-between mb-4">
            <Skeleton width={24} height={24} />
            <Skeleton width={60} height={20} />
          </div>
          <Skeleton height={32} width="80%" className="mb-2" />
          <Skeleton height={16} width="60%" />
        </div>
      </div>
    ))}
  </div>
);

// Page Skeleton
export const PageSkeleton: React.FC<{ className?: string }> = ({ className = '' }) => (
  <div className={`min-h-screen bg-gray-50 ${className}`}>
    <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      <div className="animate-pulse">
        {/* Header */}
        <div className="mb-8">
          <Skeleton height={40} width="30%" className="mb-4" />
          <Skeleton height={20} width="50%" />
        </div>
        
        {/* Stats */}
        <StatsSkeleton className="mb-8" />
        
        {/* Content */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          <div className="lg:col-span-2">
            <TableSkeleton />
          </div>
          <div>
            <CardSkeleton />
          </div>
        </div>
      </div>
    </div>
  </div>
);

export default Skeleton;
