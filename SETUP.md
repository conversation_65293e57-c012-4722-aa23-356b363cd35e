# QuickDash Setup Guide

## 🚀 Quick Start (Demo Mode)

The app is already configured to run in demo mode without any API keys:

```bash
npm install
npm run dev
```

Visit http://localhost:5174 to see the app running with fallback components.

## 🔧 Full Setup with Real APIs

### 1. Google Maps API Setup

1. Go to [Google Cloud Console](https://console.cloud.google.com/)
2. Create a new project or select existing one
3. Enable the following APIs:
   - Maps JavaScript API
   - Places API
   - Geocoding API
4. Create credentials (API Key)
5. Add your domain to API key restrictions
6. Copy the API key to `.env.local`:

```env
VITE_GOOGLE_MAPS_API_KEY=AIzaSyBxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx
```

### 2. Firebase Authentication Setup

1. Go to [Firebase Console](https://console.firebase.google.com/)
2. Create a new project
3. Enable Authentication and choose sign-in methods
4. Enable Firestore Database
5. Go to Project Settings > General > Your apps
6. Add a web app and copy the config
7. Add to `.env.local`:

```env
VITE_FIREBASE_API_KEY=AIzaSyBxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx
VITE_FIREBASE_AUTH_DOMAIN=your-project.firebaseapp.com
VITE_FIREBASE_PROJECT_ID=your-project-id
VITE_FIREBASE_STORAGE_BUCKET=your-project.appspot.com
VITE_FIREBASE_MESSAGING_SENDER_ID=123456789012
VITE_FIREBASE_APP_ID=1:123456789012:web:abcdef123456789012345
```

### 3. Stripe Payment Setup

1. Go to [Stripe Dashboard](https://dashboard.stripe.com/)
2. Get your publishable key from API keys section
3. Add to `.env.local`:

```env
VITE_STRIPE_PUBLISHABLE_KEY=pk_test_xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx
```

### 4. Backend API Setup

For a complete setup, you'll need a backend API. The app expects endpoints at:

- `POST /api/auth/login` - User authentication
- `POST /api/auth/register` - User registration
- `GET /api/auth/me` - Get current user
- `POST /api/parcels` - Create parcel
- `GET /api/parcels` - List parcels
- `GET /api/parcels/track/:id` - Track parcel
- `POST /api/partners/register` - Register delivery partner

Update the API base URL:

```env
VITE_API_BASE_URL=https://your-api-domain.com/api
```

## 🧪 Testing Setup

Run tests with:

```bash
npm test
npm run test:coverage
```

## 🐳 Docker Setup

### Development
```bash
docker-compose --profile dev up
```

### Production
```bash
docker-compose up
```

## 🚀 Deployment

### Vercel
1. Connect your GitHub repository to Vercel
2. Add environment variables in Vercel dashboard
3. Deploy automatically on push to main

### Manual Build
```bash
npm run build
```

The `dist/` folder contains the production build.

## 🔒 Security Notes

- Never commit real API keys to version control
- Use environment variables for all sensitive data
- Enable API key restrictions in Google Cloud Console
- Set up proper CORS policies for your backend
- Use HTTPS in production

## 🆘 Troubleshooting

### Google Maps not loading
- Check if `VITE_GOOGLE_MAPS_API_KEY` is set
- Verify API key has correct permissions
- Check browser console for API errors

### Authentication not working
- Verify Firebase configuration
- Check if Authentication is enabled in Firebase Console
- Ensure correct domain is added to authorized domains

### Payment errors
- Verify Stripe publishable key
- Check if test mode is enabled for development
- Ensure backend has corresponding secret key

### API connection issues
- Check if backend is running
- Verify `VITE_API_BASE_URL` is correct
- Check CORS configuration on backend

## 📞 Support

For issues or questions:
- Check the main README.md
- Create an issue in the repository
- Contact: <EMAIL>
