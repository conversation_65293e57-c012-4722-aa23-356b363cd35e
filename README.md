# QuickDash - Hyperlocal Parcel Delivery Service

A modern, full-stack React TypeScript application for hyperlocal parcel delivery services with real-time tracking, payment integration, and comprehensive admin dashboard.

## 🚀 Features Implemented

### ✅ **Real Backend API Integration**
- **Axios-based API service** with interceptors for authentication
- **Comprehensive error handling** and retry mechanisms
- **Type-safe API calls** with TypeScript interfaces
- **Token-based authentication** with automatic refresh

### ✅ **Comprehensive Testing Suite**
- **Jest + React Testing Library** setup
- **Unit tests** for utilities and components
- **Redux store testing** with mock services
- **Error boundary testing** with proper mocking
- **Test coverage reporting** and CI/CD ready

### ✅ **Google Maps API Integration**
- **Real Google Maps** with API key configuration
- **Interactive markers** and info windows
- **Traffic layer support** for real-time traffic data
- **Geolocation services** for pickup/delivery tracking
- **Fallback UI** when API is not configured

### ✅ **User Authentication & Authorization**
- **Firebase Authentication** integration
- **Role-based access control** (Customer, Partner, Admin)
- **Protected routes** with automatic redirects
- **JWT token management** with localStorage
- **Password reset** and email verification

### ✅ **Stripe Payment Integration**
- **Secure payment processing** with Stripe Elements
- **Multiple payment methods** support
- **Real-time payment status** updates
- **Payment intent creation** and confirmation
- **Error handling** for failed payments

### ✅ **Error Boundaries & Error Handling**
- **React Error Boundary** for graceful error handling
- **Development vs Production** error displays
- **Automatic error logging** to external services
- **User-friendly error messages** with recovery options
- **Global error state management**

### ✅ **Redux State Management**
- **Redux Toolkit** for efficient state management
- **Async thunks** for API calls
- **Normalized state structure** for optimal performance
- **Type-safe selectors** and actions
- **Persistent state** with localStorage integration

### ✅ **Loading Skeletons & UX**
- **Skeleton components** for all major UI elements
- **Loading states** for async operations
- **Smooth transitions** and animations
- **Responsive design** for all screen sizes
- **Accessibility features** with proper ARIA labels

## 🛠️ Tech Stack

### Frontend
- **React 18.3.1** with TypeScript 5.5.3
- **Vite 5.4.2** for fast development and building
- **Tailwind CSS 3.4.1** for styling
- **Redux Toolkit** for state management
- **React Router DOM** for navigation
- **React Hook Form** with Zod validation

### Backend Integration
- **Axios** for HTTP requests
- **Firebase** for authentication and real-time data
- **Stripe** for payment processing
- **Google Maps API** for location services

### Testing
- **Jest 29.7.0** with TypeScript support
- **React Testing Library** for component testing
- **User Event** for interaction testing
- **Coverage reporting** with multiple formats

### Development Tools
- **ESLint** with TypeScript rules
- **PostCSS** with Autoprefixer
- **TypeScript** with strict configuration
- **Vite** with React plugin

## 📁 Project Structure

```
src/
├── components/           # Reusable UI components
│   ├── auth/            # Authentication components
│   ├── payment/         # Payment-related components
│   ├── __tests__/       # Component tests
│   ├── ErrorBoundary.tsx
│   ├── LoadingSkeleton.tsx
│   ├── Notifications.tsx
│   └── GoogleMap.tsx
├── config/              # Configuration files
│   ├── env.ts          # Environment variables
│   └── firebase.ts     # Firebase configuration
├── pages/               # Page components
│   ├── HomePage.tsx
│   ├── BookParcelPage.tsx
│   ├── TrackParcelPage.tsx
│   ├── AdminDashboard.tsx
│   ├── LoginPage.tsx
│   └── ...
├── services/            # API services
│   ├── api.ts          # Base API service
│   ├── authService.ts  # Authentication API
│   ├── parcelService.ts # Parcel management API
│   └── partnerService.ts # Partner management API
├── store/               # Redux store
│   ├── index.ts        # Store configuration
│   ├── slices/         # Redux slices
│   └── __tests__/      # Store tests
├── types/               # TypeScript type definitions
├── utils/               # Utility functions
│   ├── helpers.ts      # Helper functions
│   ├── constants.ts    # Application constants
│   └── __tests__/      # Utility tests
└── setupTests.ts        # Test configuration
```

## 🚀 Getting Started

### Prerequisites
- Node.js 18+ and npm
- Google Maps API key
- Firebase project
- Stripe account (for payments)

### Installation

1. **Clone the repository**
```bash
git clone <repository-url>
cd quickdash-delivery-app
```

2. **Install dependencies**
```bash
npm install
```

3. **Environment Setup**
```bash
cp .env.example .env.local
```

4. **Configure environment variables** in `.env.local`:
```env
# API Configuration
VITE_API_BASE_URL=http://localhost:3001/api

# Google Maps API Key
VITE_GOOGLE_MAPS_API_KEY=your_google_maps_api_key_here

# Firebase Configuration
VITE_FIREBASE_API_KEY=your_firebase_api_key_here
VITE_FIREBASE_AUTH_DOMAIN=your_project.firebaseapp.com
VITE_FIREBASE_PROJECT_ID=your_project_id
VITE_FIREBASE_STORAGE_BUCKET=your_project.appspot.com
VITE_FIREBASE_MESSAGING_SENDER_ID=your_sender_id
VITE_FIREBASE_APP_ID=your_app_id

# Stripe Configuration
VITE_STRIPE_PUBLISHABLE_KEY=pk_test_your_stripe_publishable_key_here
```

### Development

```bash
# Start development server
npm run dev

# Run tests
npm test

# Run tests in watch mode
npm run test:watch

# Generate test coverage
npm run test:coverage

# Build for production
npm run build

# Preview production build
npm run preview
```

## 🧪 Testing

The application includes comprehensive testing:

```bash
# Run all tests
npm test

# Run tests with coverage
npm run test:coverage

# Run tests in watch mode
npm run test:watch
```

### Test Coverage
- **Components**: Error boundaries, forms, UI components
- **Services**: API calls, authentication, data fetching
- **Store**: Redux slices, async thunks, state management
- **Utils**: Helper functions, validation, calculations

## 🔧 Configuration

### API Keys Setup

1. **Google Maps API**:
   - Go to [Google Cloud Console](https://console.cloud.google.com/)
   - Enable Maps JavaScript API and Places API
   - Create credentials and add to `.env.local`

2. **Firebase**:
   - Create project at [Firebase Console](https://console.firebase.google.com/)
   - Enable Authentication and Firestore
   - Copy config to `.env.local`

3. **Stripe**:
   - Get publishable key from [Stripe Dashboard](https://dashboard.stripe.com/)
   - Add to `.env.local`

## 🚀 Deployment

### Build for Production
```bash
npm run build
```

### Deploy to Vercel
```bash
npm install -g vercel
vercel --prod
```

### Deploy to Netlify
```bash
npm run build
# Upload dist/ folder to Netlify
```

## 📊 Performance Features

- **Code splitting** with React.lazy()
- **Image optimization** with proper loading states
- **Bundle analysis** with Vite bundle analyzer
- **Caching strategies** for API calls
- **Optimistic updates** for better UX

## 🔒 Security Features

- **Input validation** with Zod schemas
- **XSS protection** with proper sanitization
- **CSRF protection** with token validation
- **Secure headers** configuration
- **Environment variable** protection

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests for new features
5. Ensure all tests pass
6. Submit a pull request

## 📄 License

This project is licensed under the MIT License.

## 🆘 Support

For support, email <EMAIL> or create an issue in the repository.
