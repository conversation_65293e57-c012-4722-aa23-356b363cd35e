import { apiService } from './api';
import { Parcel } from '../types';

interface CreateParcelData {
  senderName: string;
  senderPhone: string;
  senderAddress: string;
  receiverName: string;
  receiverPhone: string;
  receiverAddress: string;
  packageType: string;
  weight: number;
  dimensions: {
    length: number;
    width: number;
    height: number;
  };
  pickupDate: Date;
  deliveryDate?: Date;
  amount: number;
  paymentStatus: 'pending' | 'paid' | 'refunded';
}

interface ParcelFilters {
  page?: number;
  limit?: number;
  status?: string;
  searchTerm?: string;
  dateRange?: {
    start: Date;
    end: Date;
  };
  senderId?: string;
  partnerId?: string;
}

interface ParcelsResponse {
  parcels: Parcel[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
}

interface TrackingUpdate {
  status: string;
  location?: {
    lat: number;
    lng: number;
    address: string;
  };
  notes?: string;
}

export const parcelService = {
  // Create new parcel
  createParcel: async (parcelData: CreateParcelData): Promise<Parcel> => {
    try {
      const response = await apiService.post<Parcel>('/parcels', parcelData);
      return response;
    } catch (error) {
      throw error;
    }
  },

  // Get parcels with filters and pagination
  getParcels: async (filters: ParcelFilters = {}): Promise<ParcelsResponse> => {
    try {
      const queryParams = new URLSearchParams();
      
      Object.entries(filters).forEach(([key, value]) => {
        if (value !== undefined && value !== null) {
          if (key === 'dateRange' && typeof value === 'object') {
            queryParams.append('startDate', value.start.toISOString());
            queryParams.append('endDate', value.end.toISOString());
          } else {
            queryParams.append(key, value.toString());
          }
        }
      });

      const response = await apiService.get<ParcelsResponse>(
        `/parcels?${queryParams.toString()}`
      );
      return response;
    } catch (error) {
      throw error;
    }
  },

  // Get parcel by ID
  getParcelById: async (id: string): Promise<Parcel> => {
    try {
      const response = await apiService.get<Parcel>(`/parcels/${id}`);
      return response;
    } catch (error) {
      throw error;
    }
  },

  // Get parcel by tracking ID
  getParcelByTrackingId: async (trackingId: string): Promise<Parcel> => {
    try {
      const response = await apiService.get<Parcel>(`/parcels/track/${trackingId}`);
      return response;
    } catch (error) {
      throw error;
    }
  },

  // Update parcel status
  updateParcelStatus: async (id: string, status: string): Promise<Parcel> => {
    try {
      const response = await apiService.patch<Parcel>(`/parcels/${id}/status`, {
        status,
      });
      return response;
    } catch (error) {
      throw error;
    }
  },

  // Add tracking update
  addTrackingUpdate: async (id: string, update: TrackingUpdate): Promise<Parcel> => {
    try {
      const response = await apiService.post<Parcel>(`/parcels/${id}/tracking`, update);
      return response;
    } catch (error) {
      throw error;
    }
  },

  // Assign partner to parcel
  assignPartner: async (parcelId: string, partnerId: string): Promise<Parcel> => {
    try {
      const response = await apiService.patch<Parcel>(`/parcels/${parcelId}/assign`, {
        partnerId,
      });
      return response;
    } catch (error) {
      throw error;
    }
  },

  // Cancel parcel
  cancelParcel: async (id: string, reason?: string): Promise<Parcel> => {
    try {
      const response = await apiService.patch<Parcel>(`/parcels/${id}/cancel`, {
        reason,
      });
      return response;
    } catch (error) {
      throw error;
    }
  },

  // Get delivery estimate
  getDeliveryEstimate: async (data: {
    pickupAddress: string;
    deliveryAddress: string;
    weight: number;
    packageType: string;
  }): Promise<{
    estimatedTime: string;
    estimatedCost: number;
    distance: number;
  }> => {
    try {
      const response = await apiService.post('/parcels/estimate', data);
      return response;
    } catch (error) {
      throw error;
    }
  },

  // Get parcel statistics
  getParcelStats: async (filters?: {
    dateRange?: { start: Date; end: Date };
    partnerId?: string;
  }): Promise<{
    totalParcels: number;
    deliveredParcels: number;
    pendingParcels: number;
    cancelledParcels: number;
    totalRevenue: number;
    averageDeliveryTime: number;
  }> => {
    try {
      const queryParams = new URLSearchParams();
      
      if (filters?.dateRange) {
        queryParams.append('startDate', filters.dateRange.start.toISOString());
        queryParams.append('endDate', filters.dateRange.end.toISOString());
      }
      
      if (filters?.partnerId) {
        queryParams.append('partnerId', filters.partnerId);
      }

      const response = await apiService.get(`/parcels/stats?${queryParams.toString()}`);
      return response;
    } catch (error) {
      throw error;
    }
  },
};
