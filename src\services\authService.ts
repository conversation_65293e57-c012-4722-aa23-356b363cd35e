import { apiService } from './api';
import { User } from '../types';

interface LoginCredentials {
  email: string;
  password: string;
}

interface RegisterData {
  name: string;
  email: string;
  password: string;
  phone: string;
  role: 'customer' | 'partner';
}

interface AuthResponse {
  user: User;
  token: string;
  refreshToken?: string;
}

interface ResetPasswordData {
  email: string;
}

interface ChangePasswordData {
  currentPassword: string;
  newPassword: string;
}

export const authService = {
  // Login user
  login: async (credentials: LoginCredentials): Promise<AuthResponse> => {
    try {
      const response = await apiService.post<AuthResponse>('/auth/login', credentials);
      return response;
    } catch (error) {
      throw error;
    }
  },

  // Register user
  register: async (userData: RegisterData): Promise<AuthResponse> => {
    try {
      const response = await apiService.post<AuthResponse>('/auth/register', userData);
      return response;
    } catch (error) {
      throw error;
    }
  },

  // Logout user
  logout: async (): Promise<void> => {
    try {
      await apiService.post('/auth/logout');
    } catch (error) {
      // Even if logout fails on server, we should clear local storage
      console.warn('Logout request failed:', error);
    }
  },

  // Get current user
  getCurrentUser: async (): Promise<User> => {
    try {
      const response = await apiService.get<User>('/auth/me');
      return response;
    } catch (error) {
      throw error;
    }
  },

  // Refresh token
  refreshToken: async (refreshToken: string): Promise<AuthResponse> => {
    try {
      const response = await apiService.post<AuthResponse>('/auth/refresh', {
        refreshToken,
      });
      return response;
    } catch (error) {
      throw error;
    }
  },

  // Request password reset
  requestPasswordReset: async (data: ResetPasswordData): Promise<{ message: string }> => {
    try {
      const response = await apiService.post<{ message: string }>('/auth/forgot-password', data);
      return response;
    } catch (error) {
      throw error;
    }
  },

  // Reset password with token
  resetPassword: async (token: string, newPassword: string): Promise<{ message: string }> => {
    try {
      const response = await apiService.post<{ message: string }>('/auth/reset-password', {
        token,
        newPassword,
      });
      return response;
    } catch (error) {
      throw error;
    }
  },

  // Change password (authenticated user)
  changePassword: async (data: ChangePasswordData): Promise<{ message: string }> => {
    try {
      const response = await apiService.post<{ message: string }>('/auth/change-password', data);
      return response;
    } catch (error) {
      throw error;
    }
  },

  // Verify email
  verifyEmail: async (token: string): Promise<{ message: string }> => {
    try {
      const response = await apiService.post<{ message: string }>('/auth/verify-email', {
        token,
      });
      return response;
    } catch (error) {
      throw error;
    }
  },

  // Resend verification email
  resendVerificationEmail: async (): Promise<{ message: string }> => {
    try {
      const response = await apiService.post<{ message: string }>('/auth/resend-verification');
      return response;
    } catch (error) {
      throw error;
    }
  },

  // Update profile
  updateProfile: async (profileData: Partial<User>): Promise<User> => {
    try {
      const response = await apiService.patch<User>('/auth/profile', profileData);
      return response;
    } catch (error) {
      throw error;
    }
  },

  // Delete account
  deleteAccount: async (password: string): Promise<{ message: string }> => {
    try {
      const response = await apiService.delete<{ message: string }>('/auth/account', {
        data: { password },
      });
      return response;
    } catch (error) {
      throw error;
    }
  },
};
