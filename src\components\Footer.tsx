import React from 'react';
import { Link } from 'react-router-dom';
import { Package2, Phone, Mail, MapPin, Facebook, Twitter, Instagram, Linkedin } from 'lucide-react';

const Footer: React.FC = () => {
  return (
    <footer className="bg-gray-900 text-white">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
          {/* Company Info */}
          <div className="space-y-4">
            <div className="flex items-center space-x-2">
              <div className="bg-gradient-to-r from-blue-600 to-emerald-600 p-2 rounded-lg">
                <Package2 className="h-6 w-6 text-white" />
              </div>
              <span className="text-xl font-bold">QuickDash</span>
            </div>
            <p className="text-gray-400 text-sm">
              Your trusted hyperlocal parcel delivery service. Fast, reliable, and affordable delivery within your city.
            </p>
            <div className="flex space-x-4">
              <Facebook className="h-5 w-5 text-gray-400 hover:text-blue-500 cursor-pointer transition-colors" />
              <Twitter className="h-5 w-5 text-gray-400 hover:text-blue-400 cursor-pointer transition-colors" />
              <Instagram className="h-5 w-5 text-gray-400 hover:text-pink-500 cursor-pointer transition-colors" />
              <Linkedin className="h-5 w-5 text-gray-400 hover:text-blue-600 cursor-pointer transition-colors" />
            </div>
          </div>

          {/* Quick Links */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold">Quick Links</h3>
            <div className="space-y-2">
              <Link to="/book" className="block text-gray-400 hover:text-white transition-colors">Book Parcel</Link>
              <Link to="/track" className="block text-gray-400 hover:text-white transition-colors">Track Package</Link>
              <Link to="/app" className="block text-gray-400 hover:text-white transition-colors">Mobile App</Link>
              <Link to="/partner" className="block text-gray-400 hover:text-white transition-colors">Become Partner</Link>
            </div>
          </div>

          {/* Services */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold">Services</h3>
            <div className="space-y-2">
              <p className="text-gray-400">Same Day Delivery</p>
              <p className="text-gray-400">Express Delivery</p>
              <p className="text-gray-400">Document Delivery</p>
              <p className="text-gray-400">Bulk Orders</p>
            </div>
          </div>

          {/* Contact */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold">Contact Us</h3>
            <div className="space-y-3">
              <div className="flex items-center space-x-2">
                <Phone className="h-4 w-4 text-blue-500" />
                <span className="text-gray-400">+91 98765 43210</span>
              </div>
              <div className="flex items-center space-x-2">
                <Mail className="h-4 w-4 text-blue-500" />
                <span className="text-gray-400"><EMAIL></span>
              </div>
              <div className="flex items-center space-x-2">
                <MapPin className="h-4 w-4 text-blue-500" />
                <span className="text-gray-400">Available in major cities</span>
              </div>
            </div>
          </div>
        </div>

        <div className="border-t border-gray-800 mt-8 pt-8 text-center">
          <p className="text-gray-400">&copy; 2025 QuickDash. All rights reserved.</p>
        </div>
      </div>
    </footer>
  );
};

export default Footer;